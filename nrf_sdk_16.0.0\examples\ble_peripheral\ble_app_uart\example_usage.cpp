/**
 * @file example_usage.cpp
 * @brief Example usage of the Central/Peripheral BLE functionality
 * 
 * This file demonstrates how to use the new central and peripheral
 * functionality in the ble_app_uart application.
 */

#include "ble_central_peripheral.h"
#include "yr_util.h"

/**
 * @brief Example function showing how to use central/peripheral modes
 */
void example_central_peripheral_usage(void) {
    
    // 1. Enable central mode (this should be done early in main())
    setCentralMode(true);
    
    // 2. Check if central mode is enabled
    if (getCentralMode()) {
        NRF_LOG_INFO("Central mode is enabled");
    }
    
    // 3. Check connection status
    bool central_connected = isCentralConnected();
    bool peripheral_connected = isPeripheralConnected();
    
    NRF_LOG_INFO("Central connected: %s", central_connected ? "YES" : "NO");
    NRF_LOG_INFO("Peripheral connected: %s", peripheral_connected ? "YES" : "NO");
    
    // 4. Send data via central connection (if connected)
    if (central_connected) {
        uint8_t central_data[] = "Hello from Central!";
        uint32_t result = sendBluetoothMsgCentral(central_data, sizeof(central_data) - 1);
        
        if (result == NRF_SUCCESS) {
            NRF_LOG_INFO("Data sent successfully via central connection");
        } else {
            NRF_LOG_ERROR("Failed to send data via central connection: 0x%X", result);
        }
    }
    
    // 5. Send data via peripheral connection (if connected)
    if (peripheral_connected) {
        uint8_t peripheral_data[] = "Hello from Peripheral!";
        uint32_t result = sendBluetoothMsgProxy(peripheral_data, sizeof(peripheral_data) - 1);
        
        if (result == NRF_SUCCESS) {
            NRF_LOG_INFO("Data sent successfully via peripheral connection");
        } else {
            NRF_LOG_ERROR("Failed to send data via peripheral connection: 0x%X", result);
        }
    }
    
    // 6. Run the test function
    testCentralPeripheralMode();
}

/**
 * @brief Example of how to handle different scenarios
 */
void example_scenarios(void) {
    
    // Scenario 1: Only peripheral mode (original behavior)
    setCentralMode(false);
    NRF_LOG_INFO("Running in peripheral-only mode");
    
    // Scenario 2: Both central and peripheral modes
    setCentralMode(true);
    NRF_LOG_INFO("Running in dual mode (central + peripheral)");
    
    // Scenario 3: Check and respond to connection events
    if (isCentralConnected() && isPeripheralConnected()) {
        NRF_LOG_INFO("Both connections active - device is acting as bridge");
        
        // Example: Forward data between connections
        // (This would typically be done in the data handler)
        uint8_t bridge_msg[] = "Bridge mode active";
        sendBluetoothMsgCentral(bridge_msg, sizeof(bridge_msg) - 1);
        sendBluetoothMsgProxy(bridge_msg, sizeof(bridge_msg) - 1);
    }
    
    // Scenario 4: Handle single connections
    if (isCentralConnected() && !isPeripheralConnected()) {
        NRF_LOG_INFO("Only central connection active");
    }
    
    if (!isCentralConnected() && isPeripheralConnected()) {
        NRF_LOG_INFO("Only peripheral connection active");
    }
    
    if (!isCentralConnected() && !isPeripheralConnected()) {
        NRF_LOG_INFO("No connections active - scanning and advertising");
    }
}

/**
 * @brief Example of periodic status check
 * Call this function periodically (e.g., every 5 seconds) to monitor status
 */
void example_periodic_status_check(void) {
    static uint32_t check_counter = 0;
    check_counter++;
    
    NRF_LOG_INFO("=== Status Check #%d ===", check_counter);
    
    // Log current configuration
    NRF_LOG_INFO("Central mode: %s", getCentralMode() ? "ON" : "OFF");
    
    // Log connection status
    NRF_LOG_INFO("Connections - Central: %s, Peripheral: %s",
                 isCentralConnected() ? "CONNECTED" : "DISCONNECTED",
                 isPeripheralConnected() ? "CONNECTED" : "DISCONNECTED");
    
    // Send periodic heartbeat if connected
    if (isCentralConnected()) {
        uint8_t heartbeat[] = "Central Heartbeat";
        sendBluetoothMsgCentral(heartbeat, sizeof(heartbeat) - 1);
    }
    
    if (isPeripheralConnected()) {
        uint8_t heartbeat[] = "Peripheral Heartbeat";
        sendBluetoothMsgProxy(heartbeat, sizeof(heartbeat) - 1);
    }
}

/**
 * @brief Example of how to integrate with existing application logic
 */
void example_integration_with_app_main_board(void) {
    
    // This function shows how you might integrate the new functionality
    // with your existing app_main_board logic
    
    // Check if we have any BLE connections
    bool any_connection = isCentralConnected() || isPeripheralConnected();
    
    if (any_connection) {
        // Handle connected state
        // You can call app_main_board.handleConnected() or similar
        
        // Example: Send sensor data to all connected devices
        uint8_t sensor_data[] = "Sensor: 25.6C, 60%RH";
        
        if (isCentralConnected()) {
            sendBluetoothMsgCentral(sensor_data, sizeof(sensor_data) - 1);
        }
        
        if (isPeripheralConnected()) {
            sendBluetoothMsgProxy(sensor_data, sizeof(sensor_data) - 1);
        }
    } else {
        // Handle disconnected state
        // You can call app_main_board.handleDisconnected() or similar
        NRF_LOG_INFO("No BLE connections - device in standalone mode");
    }
}

/**
 * @brief Example of error handling
 */
void example_error_handling(void) {
    
    // Example 1: Handle send failures
    uint8_t test_data[] = "Test message";
    
    if (isCentralConnected()) {
        uint32_t result = sendBluetoothMsgCentral(test_data, sizeof(test_data) - 1);
        
        switch (result) {
            case NRF_SUCCESS:
                NRF_LOG_INFO("Central TX: Success");
                break;
            case NRF_ERROR_INVALID_STATE:
                NRF_LOG_WARNING("Central TX: Invalid state (not connected?)");
                break;
            case NRF_ERROR_RESOURCES:
                NRF_LOG_WARNING("Central TX: No resources (buffer full?)");
                break;
            default:
                NRF_LOG_ERROR("Central TX: Error 0x%X", result);
                break;
        }
    }
    
    // Example 2: Validate central mode before operations
    if (!getCentralMode()) {
        NRF_LOG_WARNING("Central mode is disabled - central operations not available");
        return;
    }
    
    // Example 3: Handle connection state changes
    static bool prev_central_connected = false;
    static bool prev_peripheral_connected = false;
    
    bool current_central = isCentralConnected();
    bool current_peripheral = isPeripheralConnected();
    
    // Detect connection state changes
    if (current_central != prev_central_connected) {
        if (current_central) {
            NRF_LOG_INFO("Central connection established");
        } else {
            NRF_LOG_INFO("Central connection lost");
        }
        prev_central_connected = current_central;
    }
    
    if (current_peripheral != prev_peripheral_connected) {
        if (current_peripheral) {
            NRF_LOG_INFO("Peripheral connection established");
        } else {
            NRF_LOG_INFO("Peripheral connection lost");
        }
        prev_peripheral_connected = current_peripheral;
    }
}
