/**
 * @file test_data_routing.h
 * @brief Header file for BLE Central+Peripheral data routing test functions
 */

#ifndef TEST_DATA_ROUTING_H_
#define TEST_DATA_ROUTING_H_

// Use Nordic SDK types instead of standard library
#ifndef uint8_t
typedef unsigned char uint8_t;
#endif
#ifndef uint16_t
typedef unsigned short uint16_t;
#endif
#ifndef uint32_t
typedef unsigned int uint32_t;
#endif


#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Test basic connectivity functionality
 */
void test_basic_connectivity(void);

/**
 * @brief Test data forwarding functionality
 */
void test_data_forwarding(void);

/**
 * @brief Test heartbeat and status functionality
 */
void test_heartbeat_status(void);

/**
 * @brief Test protocol data types
 */
void test_protocol_data_types(void);

/**
 * @brief Run comprehensive test suite
 */
void run_comprehensive_test(void);

/**
 * @brief Enable/disable automatic test mode
 * 
 * @param[in] enable  True to enable test mode, false to disable
 */
void set_test_mode(bool enable);

/**
 * @brief Check if it's time to run tests (call this periodically from main loop)
 */
void check_test_schedule(void);

/**
 * @brief Manual test trigger (for debugging)
 */
void trigger_manual_test(void);

/**
 * @brief Test connection management functionality
 */
void test_connection_management(void);

/**
 * @brief Initialize test system
 */
void init_test_system(void);

#ifdef __cplusplus
}
#endif

#endif // TEST_DATA_ROUTING_H_
