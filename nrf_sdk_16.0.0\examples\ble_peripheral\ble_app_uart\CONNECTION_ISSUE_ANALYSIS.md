# BLE连接问题深度分析

## 当前问题总结

从您的日志分析，发现了以下关键问题：

### 1. 设备名称解析失败
```
Raw adv data (11 bytes):
  [05]: 0x59  // 'Y'
  [06]: 0x52  // 'R'  
  [07]: 0x6F  // 'o'
  [08]: 0x62  // 'b'
  [09]: 0x6F  // 'o'
  [10]: 0x74  // 't'
Found device:  (name_found: 0)
```

**分析**：广播数据包含"YRobot"，但SDK的`ble_advdata_name_find`函数解析失败。

### 2. 连接参数问题
- 第一次尝试：`0x12` (NRF_ERROR_INVALID_STATE)
- 第二次尝试：`0x10` (NRF_ERROR_INVALID_PARAM)

**分析**：连接参数被目标设备拒绝。

### 3. 信号强度问题
```
RSSI: -88 dBm
```

**分析**：信号强度较弱，可能影响连接稳定性。

## 已实施的修复

### 1. 手动广播数据解析
```cpp
// 手动解析广播数据中的设备名称
while (pos < p_adv->data.len) {
  uint8_t length = p_adv->data.p_data[pos];
  uint8_t type = p_adv->data.p_data[pos + 1];
  
  if (type == 0x09 || type == 0x08) {  // Complete/Shortened Local Name
    // 提取设备名称
  }
}
```

### 2. 信号强度过滤
```cpp
if (p_adv->rssi < -80) {
  NRF_LOG_WARNING("Signal too weak (RSSI: %d), skipping connection", p_adv->rssi);
  return;
}
```

### 3. 三重连接尝试策略
1. **自定义参数**：50-100ms连接间隔
2. **默认参数**：NULL参数让SoftDevice选择
3. **最小参数**：20-40ms连接间隔

### 4. 改进的扫描停止
```cpp
ret_code_t stop_err = sd_ble_gap_scan_stop();  // 直接调用SoftDevice
nrf_delay_ms(200);  // 更长的延迟
```

## 预期的新日志输出

测试后应该看到：

```
Raw adv data (11 bytes):
  [00]: 0x02
  [01]: 0x01
  [02]: 0x06
  [03]: 0x07
  [04]: 0x09
  [05]: 0x59  // 'Y'
  [06]: 0x52  // 'R'
  [07]: 0x6F  // 'o'
  [08]: 0x62  // 'b'
  [09]: 0x6F  // 'o'
  [10]: 0x74  // 't'
Manual name extraction: YRobot (type: 0x09, len: 6)
Found device: YRobot (name_found: 1)
MAC: CB:A8:54:52:CC:6C
RSSI: -88 dBm, Data len: 11
Signal too weak (RSSI: -88), skipping connection
```

或者如果信号足够强：

```
Stopped scanning (result: 0x00), attempting connection...
Connection params: min=40, max=80, latency=0, timeout=600
Attempting connection with custom parameters...
Connection request sent successfully
BLE Connected as Central
```

## 可能的结果分析

### 结果A：信号强度过滤生效
如果看到"Signal too weak"消息，说明：
- 设备名称解析已修复
- 信号强度过滤正常工作
- 需要将设备移近或增加发射功率

### 结果B：连接成功
如果看到"Connection request sent successfully"和"BLE Connected as Central"：
- 问题已完全解决
- 可以开始测试数据传输

### 结果C：仍然连接失败
如果三次尝试都失败，可能的原因：
1. **目标设备问题**：设备不接受连接或已连接到其他设备
2. **硬件问题**：RF路径、天线或供电问题
3. **软件配置问题**：SoftDevice配置或内存分配

## 进一步的故障排除

### 1. 如果设备名称仍然解析失败

检查广播数据格式：
```
[03]: 0x07  // 长度 = 7
[04]: 0x09  // 类型 = Complete Local Name
[05-10]: YRobot  // 6字节的名称数据
```

这个格式是正确的，手动解析应该能成功。

### 2. 如果连接仍然失败

**方案A：检查目标设备**
- 确认设备确实可连接
- 尝试用手机连接该设备
- 检查设备是否已连接到其他设备

**方案B：检查SoftDevice配置**
在`sdk_config.h`中确认：
```c
#define NRF_SDH_BLE_CENTRAL_LINK_COUNT 1
#define NRF_SDH_BLE_PERIPHERAL_LINK_COUNT 1
#define NRF_SDH_BLE_TOTAL_LINK_COUNT 2
```

**方案C：使用最简单的测试**
创建一个专用的peripheral设备进行测试：
```cpp
// 在另一个nRF52设备上运行标准ble_app_uart
#define DEVICE_NAME "YRobot"
```

### 3. 如果信号强度问题

**解决方案**：
1. 将设备移近（<1米）
2. 检查天线连接
3. 增加发射功率（如果可能）
4. 降低RSSI阈值：
```cpp
if (p_adv->rssi < -90) {  // 从-80改为-90
```

## 测试建议

### 1. 立即测试
重新编译并观察新的日志输出，特别关注：
- 设备名称是否正确解析
- 信号强度过滤是否生效
- 连接尝试的详细过程

### 2. 分步测试
1. **第一步**：确认设备名称解析正常
2. **第二步**：如果信号太弱，移近设备重试
3. **第三步**：观察连接尝试过程

### 3. 对比测试
使用手机的nRF Connect应用：
1. 扫描同一设备
2. 查看设备信息
3. 尝试连接
4. 对比结果

## 成功标志

连接成功时应该看到：
```
Manual name extraction: YRobot (type: 0x09, len: 6)
Found device: YRobot (name_found: 1)
RSSI: -XX dBm (>= -80)
Stopped scanning (result: 0x00)
Attempting connection with custom parameters...
Connection request sent successfully
BLE Connected as Central
```

这次的修复应该能解决设备名称解析问题，并通过多重尝试提高连接成功率。
