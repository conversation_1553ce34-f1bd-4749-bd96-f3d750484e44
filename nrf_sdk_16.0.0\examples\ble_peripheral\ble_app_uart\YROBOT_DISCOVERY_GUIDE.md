# YRobot设备发现功能使用指南

## 功能概述

该功能可以扫描并发现所有广播名称中包含"YRobot"字符串的BLE设备，并打印出它们的名称、MAC地址和信号强度，但不进行连接。

## 主要特性

1. **智能过滤**：只显示名称中包含"YRobot"的设备（不区分大小写）
2. **去重功能**：同一设备只显示一次，避免重复打印
3. **详细信息**：显示设备名称、MAC地址、RSSI信号强度
4. **统计功能**：显示发现的设备总数
5. **控制接口**：可以启动/停止扫描，查看结果

## API函数

### 控制函数

```cpp
// 启用central模式（必须先调用）
setCentralMode(1);

// 开始YRobot设备发现
startYRobotDiscovery();

// 停止发现并显示结果汇总
stopYRobotDiscovery();

// 获取已发现设备数量
int count = getDiscoveredDevicesCount();
```

### 状态查询

```cpp
// 检查central模式是否启用
if (getCentralMode()) {
    // central模式已启用
}
```

## 使用步骤

### 1. 启用Central模式

```cpp
// 在main函数中或初始化时调用
setCentralMode(1);  // 启用central模式
```

### 2. 开始设备发现

```cpp
// 开始扫描YRobot设备
startYRobotDiscovery();
```

### 3. 查看实时发现结果

设备发现时会自动打印：
```
=== NEW YRobot Device Found ===
Name: YRobot_Device_001
MAC:  AA:BB:CC:DD:EE:FF
RSSI: -45 dBm
Total found: 1
==============================
```

### 4. 停止扫描并查看汇总

```cpp
// 停止扫描并显示所有发现的设备
stopYRobotDiscovery();
```

输出示例：
```
=== Discovered YRobot Devices Summary ===
Total devices found: 3
[1] YRobot_Device_001 - AA:BB:CC:DD:EE:FF (RSSI: -45 dBm)
[2] YRobot_Sensor_02 - 11:22:33:44:55:66 (RSSI: -52 dBm)
[3] MyYRobotController - 77:88:99:AA:BB:CC (RSSI: -38 dBm)
=======================================
```

## 完整使用示例

```cpp
void example_yrobot_discovery(void) {
    // 1. 启用central模式
    setCentralMode(1);
    
    // 2. 检查模式是否启用成功
    if (getCentralMode()) {
        NRF_LOG_INFO("Central mode enabled, starting discovery...");
        
        // 3. 开始发现YRobot设备
        startYRobotDiscovery();
        
        // 4. 等待一段时间让设备扫描
        // 在实际应用中，可以通过定时器或用户输入来控制
        
        // 5. 检查发现的设备数量
        int count = getDiscoveredDevicesCount();
        NRF_LOG_INFO("Currently discovered %d YRobot devices", count);
        
        // 6. 停止扫描并显示结果（可以在需要时调用）
        // stopYRobotDiscovery();
    } else {
        NRF_LOG_ERROR("Failed to enable central mode");
    }
}
```

## 定时控制示例

```cpp
// 扫描30秒后自动停止
void timed_discovery_example(void) {
    setCentralMode(1);
    startYRobotDiscovery();
    
    // 设置30秒定时器
    app_timer_start(discovery_timer_id, APP_TIMER_TICKS(30000), NULL);
}

// 定时器回调函数
void discovery_timer_handler(void *p_context) {
    stopYRobotDiscovery();
    NRF_LOG_INFO("Discovery completed after 30 seconds");
}
```

## 配置选项

### 修改搜索关键字

如果需要搜索其他关键字，修改 `scan_evt_handler` 函数中的条件：

```cpp
// 将 "YRobot" 改为其他关键字
if (string_contains(dev_name, "YourKeyword")) {
    // ...
}
```

### 调整设备列表大小

修改 `main.cpp` 中的宏定义：

```cpp
#define MAX_DISCOVERED_DEVICES 50  // 增加到50个设备
```

### 修改设备名称缓冲区大小

```cpp
char dev_name[128] = {0};  // 增加到128字节
```

## 注意事项

1. **必须先启用Central模式**：调用 `startYRobotDiscovery()` 前必须先调用 `setCentralMode(1)`

2. **设备去重**：同一MAC地址的设备只会显示一次，即使它发送多个广播包

3. **大小写不敏感**：搜索"YRobot"时，"yrobot"、"YROBOT"、"YRobot"都会匹配

4. **内存限制**：默认最多存储20个设备，超出后不再添加新设备

5. **扫描持续性**：扫描会持续进行直到调用 `stopYRobotDiscovery()` 或 `nrf_ble_scan_stop()`

## 故障排除

### 问题1：没有发现任何设备
- 检查是否调用了 `setCentralMode(1)`
- 确认附近有包含"YRobot"的BLE设备在广播
- 检查设备的广播名称是否正确设置

### 问题2：发现了设备但信息不完整
- 某些设备可能不在广播数据中包含完整名称
- 检查设备的广播配置

### 问题3：重复显示同一设备
- 这通常不应该发生，如果出现请检查去重逻辑

## 扩展功能

可以基于此功能扩展：

1. **连接特定设备**：在发现列表中选择设备进行连接
2. **过滤条件**：添加RSSI阈值过滤
3. **设备分类**：根据名称模式对设备分类
4. **持久化存储**：将发现的设备保存到Flash
5. **Web界面**：通过UART或BLE提供设备列表查询接口
