#ifndef APP_MAIN_BOARD_H_
#define APP_MAIN_BOARD_H_

#include <stdint.h>
#include <stddef.h>
#include "util/RingBufCPP.h"
#include "util/common.h"

#include "yr_util.h"
#include "dfu_manager.h"
#include "mock_target.h"

#include "sw_version/version.h"

// #define USE_DFU_TEST_COMM
// #define USE_MOCK_TARGET
// #define USE_MOCK_TARGET_AUTO_START

#define USE_FASTER_DFU
#define USE_NEW_SPI_PROTOCOL
#define USE_BLE_TX_RING_BUFFER
// #define TX_BUFFER_EMPTY_ONLY_ONE
#define USE_READ_REQUEST
// #define DEBUG_READ_REQUEST

// #define DEBUG_BLE_CMD_RX
// #define DEBUG_BLE_TX_ERRORS

// #define DEBUG_DFU
// #define DEBUG_DFU_BLE_BUFFER
// #define DEBUG_DFU_BLE_TX
// #define DEBUG_DFU_LARGE_PACKET
// #define DEBUG_DFU_SPI_INTERRUPT
// #define DEBUG_DFU_BUFFER
// #define DEBUG_DFU_SPI_BUFFER
// #define DEBUG_DFU_SPI_TIMER

// Debug Client -> K66
// #define DEBUG_DFU_BLE_RX
// #define DEBUG_DFU_SPI_TX

// Debug Client -> K66
// #define DEBUG_SPI_RX

// FDS (Flash Device)
#define USE_FDS

#define PRINT_COMM_RATES
#define PRINT_COMM_RATES_MSG_1
// #define PRINT_COMM_RATES_MSG_2

#ifdef __cplusplus
extern "C" {
#endif  // __cplusplus

uint32_t sendBluetoothMsgProxy(uint8_t *buf, uint16_t len);

#ifdef __cplusplus
}
#endif  // __cplusplus

// FDS: FLASH Storage
#define CONFIG_FILE (0x8010)
#define CONFIG_REC_KEY (0x7010)

// struct save in flash
typedef struct {
  uint32_t boot_count;
  char main_board_serial_number[32];
  bool config1_on;
  bool config2_on;
} yr_fds_config_struct_t;

class AppMainBoard {
public:
  DfuManager dfu_manager_;
  MockTarget mock_target_;

  typedef enum
  {
    BLE_RX = 0,
    BLE_TX,
    BLE_TX_ATTEMPT,
    SPI_RX,
    SPI_TX,
    BLE_TX_BUF_OFLOWED,
    BLE_TX_FROM_BUF,
    BLE_TX_BUF_CLEARED,
    BLE_TX_COMPLETED,
    SPI_MISSING_SEQ_NOS,
    NUM_MSG_TYPES
  } msg_types_t;

  typedef enum
  {
    SPI_DBG_TX = 0,
    SPI_DBG_RX_0,
    SPI_DBG_RX_1,
    SPI_DBG_RX_2,
    NUM_SPI_DEBUG_TIMERS
  } spi_debug_timer_t;

  typedef enum
  {
    READ_REQUEST_UNSET = 0,
    READ_REQUEST_SET
  } read_request_status_t;

  enum connection_state_t
  {
    DISCONNECTED = 0,
    CONNECTED = 1,
  };

  static constexpr uint32_t READ_REQUEST_DELAY_MS = 3;
  static constexpr uint32_t READ_REQUEST_TIMEOUT_MS = 1000;
  // static constexpr bool READ_REQUEST_ACTIVE_STATE = false;
  static constexpr bool READ_REQUEST_ACTIVE_STATE = true;

  AppMainBoard()
    : dfu_manager_(this)
    , mock_target_(this)
    , is_ble_connected_(false)
    , is_ble_tx_ready_(false)
    , spi_buffers_set_(false)
    , spi_state(SPI_LISTENING)
    , spi_tx_state_(spi_tx_state_t::SPI_TX_SENT)
    , ble_receive_state_(buffer_state_t::STATE_PROCESSED)
    , spi_receive_state_(buffer_state_t::STATE_PROCESSED)
    , read_request_status_(READ_REQUEST_UNSET)
    , using_tx_interrupt_(false)
    , time_last_received_spi_msg_(0)
    , time_last_sent_spi_msg_(0)
    , time_last_toggle_interrupt_(0)
    , time_last_received_comm_request_(0)
    , timeout_comm_request_(1000)
    , toggle_spi_interrupt_count_(0)
    , received_ack_packet_(false)
    , time_last_print_(0)
    , time_last_update_(0)
    , time_last_spi_buffers_set_(0)
    , time_last_tx_ble_(0)
    , last_tx_ble_failed_(false)
    , loop_count_(0) {
    resetSpiDebugTimers();
  }

  void resetSpiDebugTimers() {
    for (size_t i = 0; i < ARRAY_SIZE(spi_debug_timers_); i++) {
      spi_debug_timers_[i] = 0;
    }
  }

  void init();
  void InitFds();
  void RunFdsGarbageCollection();

  // FDS Read/Write
  void WriteSerialNumberToFlash(char *serial_number_str, const size_t len);
  void ReadSerialNumberFromFlash();

  // SPI TX: e.g. connection state change
  void SendConnectionState(const connection_state_t &state);

  void update();
  void runSlowLoop();

  void resetK66Pin();

  // SPI RX
  void handleSpiRx(const size_t &len);
  void handleSpiRxMsg(uint8_t *buf, const size_t &len);
  void processSpiRxMsg(uint8_t *buffer, const size_t &len);
  void processSpiRxMsg();
  void ProcessSerialNumberCmd(uint8_t *buf, const size_t len);
  void UpdateBleAdvertisingName();
  bool addToSpiRxBuffer(uint8_t *buf, const size_t &len);
  bool emptySpiRxBuffer();


  // SPI TX
  void writeSpiDataTxBootloader(uint8_t *buffer, const size_t &len);
  bool addToSpiTxBuffer(uint8_t *buf, const size_t &len);
  bool emptySpiTxBuffer();
  uint32_t setSpiBuffers(const bool &tx = false);

  // SPI UTIL
  bool setUseTxInterrupt(const bool &state);
  void enableBleIntPin(const bool &enable);
  void toggleSpiInterrupt();
  bool isEmptyFrame();
  void printSpiBuffers();

  // BLE RX
  void processBleRxMsg();
  void handleBleRx(uint8_t *buf, const size_t &len);
  bool addToBleRxBuffer(uint8_t *buf, const size_t &len);
  bool emptyBleRxBuffer();
  size_t getSpiPacketDataLength(uint8_t *buf, const size_t &len);
  size_t getSpiPacketTotalLength(uint8_t *buf, const size_t &len);

  // BLE TX
  bool sendBluetoothMsgBuffer(uint8_t *buf, const size_t &len);
  bool sendBluetoothMsgDirect(uint8_t *buf, const size_t &len);
  bool sendBluetoothMsgDirectLen(const size_t &length_in = 20);
  void emptyBleTxRingBuffer();
  void clearBleTxRingBuffer(const char *msg);

  // BLE EVENTS
  void handleConnected();
  void handleDisconnected();
  void handleTxComplete(uint32_t count);

  // BUFFER
  void clearSpiTxBuffer();
  void clearSpiRxBuffer();
  void clearSpiRxProcessBuffer();
  void clearSpiTxProcessBuffer();
  void clearBleRxBuffer();
  void clearBleRxProcessBuffer();

  // UTIL
  void printCommStatus();
  bool checkFor2MatchingBytesSpiRxBuffer(const uint8_t &b0_in, const uint8_t &b1_in);

  uint32_t spi_debug_timers_[NUM_SPI_DEBUG_TIMERS];

  MsgTypeStat msg_stats[NUM_MSG_TYPES];

  uint8_t ble_rx_buffer[BLE_BUF_SIZE] = { 0 };
  uint8_t ble_rx_process_buffer[BLE_BUF_SIZE] = { 0 };
  uint8_t ble_tx_buffer[BLE_TX_BUF_SIZE] = { 0 };
  uint8_t spi_rx_buffer[SPI_BUF_SIZE] = { 0 };
  uint8_t spi_tx_buffer[SPI_BUF_SIZE] = { 0 };
  uint8_t spi_rx_process_buffer[SPI_BUF_SIZE] = { 0 };
  uint8_t spi_tx_process_buffer[SPI_BUF_SIZE] = { 0 };
  uint8_t spi_rx_buffer_last[SPI_BUF_SIZE] = { 0 };
  uint8_t spi_tx_buffer_last[SPI_BUF_SIZE] = { 0 };

  RingBufCPP<ble_packet_t, BLE_TX_RING_BUF_SIZE> ble_tx_packet_ringbuf;
  RingBufCPP<ble_packet_t, BLE_RX_RING_BUF_SIZE> ble_rx_packet_ringbuf;
  RingBufCPP<spi_packet_t, SPI_TX_RING_BUF_SIZE> spi_tx_packet_ringbuf;
  RingBufCPP<spi_packet_t, SPI_RX_RING_BUF_SIZE> spi_rx_packet_ringbuf;

  state_change_t is_ble_connected_;
  state_change_t is_ble_tx_ready_;
  volatile bool spi_buffers_set_;
  val_change_t<spi_state_t> spi_state;
  val_change_t<spi_tx_state_t> spi_tx_state_;
  val_change_t<buffer_state_t> ble_receive_state_;
  val_change_t<buffer_state_t> spi_receive_state_;

  static constexpr size_t kMaxSerialNumberLength = 32;
  char serial_number_str_[kMaxSerialNumberLength];

  val_change_t<read_request_status_t> read_request_status_;

  bool using_tx_interrupt_;
  volatile uint32_t time_last_received_spi_msg_;
  volatile uint32_t time_last_sent_spi_msg_;
  volatile uint32_t time_last_toggle_interrupt_;
  volatile uint32_t time_last_received_comm_request_;
  volatile uint32_t timeout_comm_request_;
  volatile uint32_t toggle_spi_interrupt_count_;
  bool received_ack_packet_;

  uint32_t time_last_print_;
  uint32_t time_last_update_;
  volatile uint32_t time_last_spi_buffers_set_;
  uint32_t time_last_tx_ble_;
  uint32_t last_tx_ble_failed_;

  uint8_t last_seq_no_;
  bool seq_no_set_ = false;

  bool fds_config_found_{ false };

private:
  uint32_t loop_count_;
};

extern AppMainBoard app_main_board;

#endif  // APP_MAIN_BOARD_H_
