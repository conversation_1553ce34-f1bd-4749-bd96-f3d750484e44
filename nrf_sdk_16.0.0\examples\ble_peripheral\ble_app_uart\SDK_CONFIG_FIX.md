# SDK配置修复指南

## 问题根源

您用nRF Connect能连接上目标设备，说明设备本身没问题。问题在于SDK配置中**Central连接数量被设置为0**。

## 关键配置修复

我已经修复了以下关键配置：

### 1. Central连接数量
```c
// 修复前（错误）
#define NRF_SDH_BLE_CENTRAL_LINK_COUNT 0

// 修复后（正确）
#define NRF_SDH_BLE_CENTRAL_LINK_COUNT 1
```

### 2. 总连接数量
```c
// 修复前
#define NRF_SDH_BLE_TOTAL_LINK_COUNT 1

// 修复后
#define NRF_SDH_BLE_TOTAL_LINK_COUNT 2
```

## 配置说明

### NRF_SDH_BLE_CENTRAL_LINK_COUNT = 1
- **含义**：允许1个central连接
- **必要性**：设置为0时无法建立central连接
- **错误**：这就是`NRF_ERROR_CONN_COUNT`的根本原因

### NRF_SDH_BLE_TOTAL_LINK_COUNT = 2
- **含义**：总共允许2个连接（1个central + 1个peripheral）
- **必要性**：支持同时作为central和peripheral

### 其他相关配置（已正确）
```c
#define NRF_SDH_BLE_PERIPHERAL_LINK_COUNT 1  // 1个peripheral连接
#define NRF_BLE_SCAN_ENABLED 1               // 扫描模块启用
#define NRF_SDH_BLE_GAP_EVENT_LENGTH 800     // GAP事件长度
#define NRF_SDH_BLE_GATT_MAX_MTU_SIZE 247    // 最大MTU大小
```

## 立即测试

### 1. 重新编译
```
Build -> Clean Solution
Build -> Rebuild Solution
```

### 2. 烧录测试
烧录新的固件到设备。

### 3. 预期结果
现在应该看到成功的连接：

```
Manual name extraction: YRobot_FE:8E (type: 0x09, len: 24)
Found device: YRobot_FE:8E (name_found: 1)
MAC: FE:8E:36:B5:36:D9
RSSI: -53 dBm, Data len: 29
Address type: 1
Adv type: 0x01, Connectable: YES
Stopped scanning (result: 0x0), attempting connection...
Connection params: min=6, max=24, latency=0, timeout=400
Original addr type: 1
Attempting connection with custom parameters...
Connection request sent successfully
BLE Connected as Central
```

## 如果仍有问题

### 检查点1：确认配置已生效
重新编译后，配置应该生效。如果仍有`NRF_ERROR_CONN_COUNT`错误，说明配置未生效。

### 检查点2：内存配置
如果有内存相关错误，可能需要调整：

```c
// 在sdk_config.h中检查这些值
#define NRF_SDH_BLE_GAP_DATA_LENGTH 251
#define NRF_SDH_BLE_GATT_MAX_MTU_SIZE 247
```

### 检查点3：SoftDevice版本
确认使用的是S132 SoftDevice，版本兼容。

## 其他可能需要的配置

如果仍有问题，可能需要检查：

### 1. 连接配置标签
```c
// 在main.cpp中确认
#define APP_BLE_CONN_CFG_TAG 1
```

### 2. 观察者配置
```c
// 如果需要，可以启用
#define NRF_SDH_BLE_OBSERVER_PRIO_LEVELS 4
```

### 3. 事件队列大小
```c
// 如果事件处理有问题
#define NRF_SDH_BLE_GAP_EVENT_LENGTH 800
```

## 成功标志

配置修复成功后，您应该看到：

1. **编译成功**：无错误
2. **连接成功**：`Connection request sent successfully`
3. **Central连接建立**：`BLE Connected as Central`
4. **数据传输**：可以通过central连接发送数据

## 验证连接功能

连接成功后，可以测试：

```cpp
// 发送数据测试
uint8_t test_data[] = "Hello from Central";
uint32_t result = sendBluetoothMsgCentral(test_data, sizeof(test_data) - 1);
if (result == NRF_SUCCESS) {
    NRF_LOG_INFO("Data sent successfully via central connection");
}
```

## 总结

**根本问题**：`NRF_SDH_BLE_CENTRAL_LINK_COUNT`设置为0
**解决方案**：设置为1，并调整总连接数为2
**预期结果**：能够成功建立central连接

这个配置修复应该能完全解决连接问题！
