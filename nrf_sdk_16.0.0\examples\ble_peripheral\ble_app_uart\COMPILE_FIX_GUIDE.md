# 编译错误修复指南

## 问题分析

您遇到的编译错误主要包括两类：

1. **宏重定义警告** - `"NRF_FPRINTF_FLAG_AUTOMATIC_CR_ON_LF_ENABLED" redefined`
2. **ISO C标准警告** - `ISO C99/C++11 requires whitespace after the macro name`

## 解决方案

### 1. 宏重定义警告

这些警告通常不会阻止编译成功，但可以通过以下方式解决：

#### 方法A: 修改sdk_config.h (推荐)
在 `pca10040/s132/config/sdk_config.h` 文件中，找到并确保以下宏只定义一次：

```c
#ifndef NRF_FPRINTF_FLAG_AUTOMATIC_CR_ON_LF_ENABLED
#define NRF_FPRINTF_FLAG_AUTOMATIC_CR_ON_LF_ENABLED 0
#endif
```

#### 方法B: 在项目设置中添加编译器标志
在Segger Embedded Studio中：
1. 右键点击项目 -> Properties
2. 在 Configuration Properties -> C/C++ -> Preprocessor 中
3. 添加预处理器定义：`NRF_FPRINTF_FLAG_AUTOMATIC_CR_ON_LF_ENABLED=0`

### 2. ISO C标准警告

这些警告是由于宏定义格式问题引起的。通常可以忽略，但如果要修复：

#### 检查宏定义格式
确保所有宏定义后面有空格：
```c
// 错误格式
#define MACRO_NAME(x) value

// 正确格式  
#define MACRO_NAME (x) value
```

### 3. 编译器设置优化

在Segger Embedded Studio中优化编译器设置：

1. **项目属性设置**：
   - 右键项目 -> Properties
   - Configuration Properties -> C/C++
   - Language Standard: 设置为 `gnu11` (C) 和 `c++11` (C++)

2. **警告级别调整**：
   - Configuration Properties -> C/C++ -> Code Generation
   - Warnings: 设置为 `All` 或 `Normal`
   - Treat Warnings as Errors: 设置为 `No`

3. **预处理器设置**：
   - 确保包含路径正确
   - 检查预处理器定义没有冲突

## 验证修复

### 1. 清理并重新编译
```
Build -> Clean Solution
Build -> Rebuild Solution
```

### 2. 检查编译输出
编译成功的标志：
- 没有错误 (Errors: 0)
- 可能有警告但不影响功能
- 生成 .hex 文件

### 3. 功能测试
编译成功后，测试新添加的central功能：

```cpp
// 在main函数中测试
setCentralMode(1);  // 启用central模式
if (getCentralMode()) {
    NRF_LOG_INFO("Central mode enabled successfully");
}
```

## 常见问题排查

### 1. 如果仍有编译错误

**检查文件包含**：
确保以下文件在项目中：
- `nrf_ble_scan.c` (应该已经包含)
- `ble_central_peripheral.h` (新添加的)

**检查头文件路径**：
确保包含路径中有：
- `../../../../../../components/ble/nrf_ble_scan`

### 2. 链接错误

如果出现链接错误，检查：
- 所有必要的库文件是否包含
- 内存配置是否正确
- SoftDevice版本是否匹配

### 3. 运行时错误

如果编译成功但运行时出错：
- 检查堆栈大小设置
- 确保SoftDevice正确初始化
- 检查BLE配置参数

## 最终验证步骤

1. **编译成功**：无错误，可能有警告
2. **烧录成功**：.hex文件正确生成并烧录
3. **功能测试**：
   - 设备可以广播 (peripheral模式)
   - 设备可以扫描 (central模式，如果启用)
   - 日志输出正常

## 备用方案

如果上述方法都不能解决问题：

1. **使用原始项目**：
   - 复制一个干净的ble_app_uart项目
   - 逐步添加central功能代码
   - 每次添加后测试编译

2. **简化实现**：
   - 暂时注释掉central相关代码
   - 确保基本功能正常
   - 逐步启用central功能

3. **检查SDK版本**：
   - 确保使用的是nRF5 SDK 16.0.0
   - 检查SoftDevice版本兼容性

## 联系支持

如果问题仍然存在，请提供：
- 完整的编译错误日志
- 使用的SDK版本
- 编译器版本和设置
- 硬件平台信息
