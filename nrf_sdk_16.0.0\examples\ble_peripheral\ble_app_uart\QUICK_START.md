# BLE Central+Peripheral 快速开始指南

## 概述

您的BLE应用程序现在已经完全支持Central+Peripheral双重模式，并实现了智能数据路由功能。设备A可以同时连接手机（作为Peripheral）和设备B（作为Central），实现数据桥接和智能转发。

## 主要改进

### ✅ 已完成的功能

1. **双重连接支持**
   - ✅ 同时支持Central和Peripheral连接
   - ✅ 自动连接管理和重连机制
   - ✅ 连接状态监控和日志

2. **智能数据路由**
   - ✅ 协议数据包格式定义
   - ✅ 基于数据来源的智能路由
   - ✅ 支持转发、本地处理、心跳、状态等数据类型

3. **连接管理**
   - ✅ 自动重连机制（Central模式）
   - ✅ 连接质量监控
   - ✅ 优雅断开连接

4. **系统功能**
   - ✅ 定期心跳发送（30秒间隔）
   - ✅ 状态信息发送（60秒间隔）
   - ✅ 完整的错误处理

5. **测试系统**
   - ✅ 自动化测试框架
   - ✅ 手动测试触发
   - ✅ 连接性测试
   - ✅ 数据转发测试

## 使用方法

### 1. 基本配置

```c
// 在main.cpp中，设备角色通过is_central变量控制
setCentralMode(true);  // 启用Central+Peripheral模式
```

### 2. 数据发送示例

```c
// 发送数据到手机
uint8_t data[] = "Hello Phone!";
sendDataToPhone(DATA_TYPE_LOCAL_PROCESS, data, simple_strlen((char*)data));

// 发送数据到设备B
uint8_t data2[] = "Hello Device B!";
sendDataToDevice(DATA_TYPE_LOCAL_PROCESS, data2, simple_strlen((char*)data2));

// 转发数据
sendDataToDevice(DATA_TYPE_FORWARD_TO_DEVICE, data, length);  // 转发到设备
sendDataToPhone(DATA_TYPE_FORWARD_TO_PHONE, data, length);   // 转发到手机
```

### 3. 测试功能

```c
// 手动运行测试
trigger_manual_test();

// 启用自动测试（每10秒运行一次）
set_test_mode(true);

// 测试特定功能
test_basic_connectivity();
test_data_forwarding();
test_heartbeat_status();
```

## 数据路由逻辑

### 来自手机的数据处理
1. **有协议头**: 根据数据类型决定处理方式
2. **无协议头**: 本地处理 + 可选转发到设备B

### 来自设备B的数据处理
1. **有协议头**: 根据数据类型决定处理方式
2. **无协议头**: 本地处理 + 可选转发到手机

### 数据类型
- `0x01`: 转发到手机
- `0x02`: 转发到设备
- `0x03`: 本地处理
- `0x04`: 心跳消息
- `0x05`: 状态信息

## 监控和调试

### 连接状态检查
```c
bool phone_connected = isPeripheralConnected();
bool device_connected = isCentralConnected();
bool central_enabled = getCentralMode();
```

### 日志输出
- 所有数据路由操作都有详细日志
- 连接状态变化会被记录
- 错误情况有相应错误日志

### 状态信息
```c
log_ble_state();  // 输出当前BLE状态
sendStatusInfo(); // 发送状态到所有连接的设备
```

## 配置选项

### 目标设备名称
```c
// 在main.cpp中修改要连接的设备名称
static char m_target_periph_name[] = "YRobot_FE:8E:36:B5:36:D9";
```

### 定时器间隔
```c
#define HEARTBEAT_INTERVAL_MS 30000    // 心跳间隔
#define STATUS_INTERVAL_MS 60000       // 状态间隔
#define RECONNECT_INTERVAL_MS 5000     // 重连间隔
```

### 测试配置
```c
#define TEST_INTERVAL_MS 10000  // 自动测试间隔
```

## 实际使用场景

### 场景1: 设备A作为数据桥接器
1. 手机连接到设备A（Peripheral）
2. 设备A连接到设备B（Central）
3. 手机发送的数据自动转发到设备B
4. 设备B的响应自动转发回手机

### 场景2: 双向数据同步
1. 两个设备都运行相同代码
2. 设备A启用Central模式连接设备B
3. 数据在两个设备间自动同步
4. 手机可以连接任一设备获取数据

### 场景3: 分布式传感器网络
1. 多个设备形成网络
2. 数据自动路由到合适的目标
3. 手机作为控制中心
4. 支持远程监控和控制

## 故障排除

### 连接问题
- 检查目标设备名称是否正确
- 确认设备在扫描范围内
- 检查连接参数配置

### 数据发送失败
- 检查连接状态
- 验证数据长度（<244字节）
- 检查返回的错误码

### 路由问题
- 验证协议头格式（0xAA开头）
- 检查数据类型定义
- 确认连接状态

## 下一步扩展

可以考虑添加的功能：
1. 数据加密和安全性
2. 更复杂的路由算法
3. 网络拓扑发现
4. 数据缓存和重传
5. 性能监控和优化

## 总结

您的BLE应用程序现在具备了完整的Central+Peripheral功能，支持智能数据路由、自动连接管理和全面的测试系统。代码结构清晰，易于扩展和维护。

使用 `trigger_manual_test()` 来验证所有功能是否正常工作！
