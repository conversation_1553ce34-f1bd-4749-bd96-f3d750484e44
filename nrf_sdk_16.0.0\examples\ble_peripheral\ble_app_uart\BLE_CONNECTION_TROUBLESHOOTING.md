# BLE连接问题故障排除指南

## 错误代码0x12 (NRF_ERROR_INVALID_STATE) 分析

您遇到的错误代码`0x12`对应`NRF_ERROR_INVALID_STATE`，这表示BLE堆栈状态不正确。

## 常见原因和解决方案

### 1. 扫描状态冲突

**问题**：在尝试连接时扫描仍在进行
**解决方案**：在连接前停止扫描

```cpp
// 修复后的代码已经包含这个逻辑
nrf_ble_scan_stop();
NRF_LOG_INFO("Stopped scanning, attempting connection...");
```

### 2. 重复连接尝试

**问题**：已经有连接在进行中时尝试新连接
**解决方案**：检查连接状态

```cpp
if (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) {
  NRF_LOG_WARNING("Already connected as central, ignoring new device");
  return;
}
```

### 3. 连接参数问题

**问题**：连接参数不兼容
**解决方案**：使用标准参数

```cpp
ble_gap_conn_params_t conn_param = {
  .min_conn_interval = MSEC_TO_UNITS(100, UNIT_1_25_MS),  // 100ms
  .max_conn_interval = MSEC_TO_UNITS(200, UNIT_1_25_MS),  // 200ms
  .slave_latency = 0,
  .conn_sup_timeout = MSEC_TO_UNITS(4000, UNIT_10_MS)     // 4s
};
```

## 调试步骤

### 1. 检查日志输出

确保看到以下日志序列：
```
Found device: [设备名称]
MAC: [MAC地址]
Stopped scanning, attempting connection...
Connection request sent successfully
```

### 2. 验证设备状态

在连接前检查：
- Central连接句柄是否为无效值
- 扫描是否正在进行
- BLE堆栈是否已初始化

### 3. 监控连接事件

观察以下BLE事件：
- `BLE_GAP_EVT_CONNECTED` - 连接成功
- `BLE_GAP_EVT_TIMEOUT` - 连接超时
- `BLE_GAP_EVT_DISCONNECTED` - 连接断开

## 改进的错误处理

### 详细错误信息

```cpp
if (err_code != NRF_SUCCESS) {
  NRF_LOG_ERROR("sd_ble_gap_connect() failed: 0x%x (%s)", err_code, 
                (err_code == NRF_ERROR_INVALID_STATE) ? "INVALID_STATE" :
                (err_code == NRF_ERROR_INVALID_PARAM) ? "INVALID_PARAM" :
                (err_code == NRF_ERROR_BUSY) ? "BUSY" : "OTHER");
  
  // 重新启动扫描
  scan_start();
}
```

### 连接超时处理

```cpp
case BLE_GAP_EVT_TIMEOUT:
  if (p_gap_evt->params.timeout.src == BLE_GAP_TIMEOUT_SRC_CONN) {
    NRF_LOG_INFO("Connection timeout - restarting scan");
    if (is_central) {
      scan_start();
    }
  }
  break;
```

## 常见问题解决

### 问题1：设备发现但无法连接

**可能原因**：
- 目标设备不接受连接
- 目标设备已连接到其他设备
- 连接参数不匹配

**解决方案**：
1. 检查目标设备是否可连接
2. 尝试不同的连接参数
3. 确保目标设备支持central连接

### 问题2：连接后立即断开

**可能原因**：
- 配对/绑定问题
- 服务发现失败
- 连接参数协商失败

**解决方案**：
1. 禁用配对要求
2. 添加服务发现逻辑
3. 监控连接参数更新事件

### 问题3：扫描无法重新启动

**可能原因**：
- BLE堆栈状态错误
- 资源未正确释放

**解决方案**：
1. 确保正确处理断开事件
2. 重置连接句柄
3. 添加状态检查

## 测试建议

### 1. 使用已知设备测试

使用另一个nRF设备作为peripheral进行测试：
- 确保peripheral正在广播
- 使用标准的ble_app_uart作为peripheral
- 检查广播名称是否匹配

### 2. 简化连接逻辑

临时简化代码进行测试：
```cpp
// 简化版本 - 只连接第一个发现的设备
static bool connection_attempted = false;

if (!connection_attempted) {
  connection_attempted = true;
  // 尝试连接
}
```

### 3. 监控RF活动

使用nRF Sniffer或类似工具监控：
- 广播包内容
- 连接请求
- 连接响应

## 配置检查

### 1. SDK配置

确保在`sdk_config.h`中：
```c
#define NRF_BLE_SCAN_ENABLED 1
#define NRF_BLE_SCAN_FILTER_ENABLE 1
#define NRF_BLE_SCAN_BUFFER_SIZE 128
```

### 2. 连接配置

检查连接配置标签：
```c
#define APP_BLE_CONN_CFG_TAG 1
```

### 3. 内存配置

确保有足够的RAM用于多连接：
- 检查连接池大小
- 验证GATT表大小
- 确认ATT MTU设置

## 下一步调试

如果问题仍然存在：

1. **添加更多日志**：在每个关键步骤添加详细日志
2. **使用调试器**：在连接函数设置断点
3. **检查硬件**：确认天线和RF路径正常
4. **测试距离**：在近距离（<1米）测试
5. **检查干扰**：关闭其他2.4GHz设备

## 成功连接的标志

当连接成功时，您应该看到：
```
Found device: [设备名称]
MAC: [MAC地址]
Stopped scanning, attempting connection...
Connection request sent successfully
BLE Connected as Central
```

如果看到这些日志，说明连接已成功建立。
