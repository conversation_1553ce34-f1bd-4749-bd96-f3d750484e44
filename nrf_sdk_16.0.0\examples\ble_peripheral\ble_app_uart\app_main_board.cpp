#include "app_main_board.h"
#include <stdint.h>
#include "yr_util.h"
#include <math.h>

//----------------------------------------------------------------------------//
//  FDS
//----------------------------------------------------------------------------//
// Array to map FDS events to strings.
static char const *fds_evt_str[] = {
  "FDS_EVT_INIT", "FDS_EVT_WRITE", "FDS_EVT_UPDATE", "FDS_EVT_DEL_RECORD", "FDS_EVT_DEL_FILE", "FDS_EVT_GC",
};

/* Dummy configuration data. */
static yr_fds_config_struct_t yr_fds_config_struct = {
  .boot_count = 0x0,
  .config1_on = false,
  .config2_on = true,
};

static fds_record_t const m_dummy_fds_record = { .file_id = CONFIG_FILE,
                                                 .key = CONFIG_REC_KEY,
                                                 .data = {
                                                     .p_data = &yr_fds_config_struct,
                                                     .length_words =
                                                         (sizeof(yr_fds_config_struct) + 3) / sizeof(uint32_t),
                                                     // The length of a record is always expressed in 4-byte units
                                                     // (words).
                                                 } };

// /* Keep track of the progress of a delete_all operation. */
static struct {
  bool delete_next;  //!< Delete next record.
  bool pending;      //!< Waiting for an fds FDS_EVT_DEL_RECORD event, to delete the next record.
} m_delete_all;

// /* Flag to check fds initialization. */
static bool volatile m_fds_initialized = false;

#ifdef __cplusplus
extern "C" {
#endif  // __cplusplus

const char *fds_err_str(ret_code_t ret) {
  /* Array to map FDS return values to strings. */
  static char const *err_str[] = {
    "FDS_ERR_OPERATION_TIMEOUT", "FDS_ERR_NOT_INITIALIZED",
    "FDS_ERR_UNALIGNED_ADDR",    "FDS_ERR_INVALID_ARG",
    "FDS_ERR_NULL_ARG",          "FDS_ERR_NO_OPEN_RECORDS",
    "FDS_ERR_NO_SPACE_IN_FLASH", "FDS_ERR_NO_SPACE_IN_QUEUES",
    "FDS_ERR_RECORD_TOO_LARGE",  "FDS_ERR_NOT_FOUND",
    "FDS_ERR_NO_PAGES",          "FDS_ERR_USER_LIMIT_REACHED",
    "FDS_ERR_CRC_CHECK_FAILED",  "FDS_ERR_BUSY",
    "FDS_ERR_INTERNAL",
  };

  return err_str[ret - NRF_ERROR_FDS_ERR_BASE];
}


static void fds_evt_handler(fds_evt_t const *p_evt) {
  if (p_evt->result == NRF_SUCCESS) {
    NRF_LOG_INFO("Event: %s received (NRF_SUCCESS)", fds_evt_str[p_evt->id]);
  } else {
    NRF_LOG_INFO("Event: %s received (%s)", fds_evt_str[p_evt->id], fds_err_str(p_evt->result));
  }

  switch (p_evt->id) {
    case FDS_EVT_INIT:
      if (p_evt->result == NRF_SUCCESS) {
        m_fds_initialized = true;
      }
      break;

    case FDS_EVT_WRITE: {
      if (p_evt->result == NRF_SUCCESS) {
        NRF_LOG_INFO("Record ID:\t0x%04x", p_evt->write.record_id);
        NRF_LOG_INFO("File ID:\t0x%04x", p_evt->write.file_id);
        NRF_LOG_INFO("Record key:\t0x%04x", p_evt->write.record_key);
      }
    } break;

    case FDS_EVT_DEL_RECORD: {
      if (p_evt->result == NRF_SUCCESS) {
        NRF_LOG_INFO("Record ID:\t0x%04x", p_evt->del.record_id);
        NRF_LOG_INFO("File ID:\t0x%04x", p_evt->del.file_id);
        NRF_LOG_INFO("Record key:\t0x%04x", p_evt->del.record_key);
      }
      m_delete_all.pending = false;
    } break;

    case FDS_EVT_GC:
      NRF_LOG_INFO("Garbage collection event");
      break;

    default:
      break;
  }
}


/**@brief   Sleep until an event is received. */
static void fds_power_manage(void) {
#ifdef SOFTDEVICE_PRESENT
  (void)sd_app_evt_wait();
#else
  // __WFE();
#endif
}


/**@brief   Wait for fds to initialize. */
static void wait_for_fds_ready(void) {
  while (!m_fds_initialized) {
    fds_power_manage();
  }
}

/**@brief   Begin deleting all records, one by one. */
void delete_all_begin(void) {
  m_delete_all.delete_next = true;
}

bool record_delete_next(void) {
  fds_find_token_t tok = { 0 };
  fds_record_desc_t desc = { 0 };
  if (fds_record_iterate(&desc, &tok) == NRF_SUCCESS) {
    ret_code_t rc = fds_record_delete(&desc);
    if (rc != NRF_SUCCESS) {
      return false;
    }
    return true;
  } else {
    // No records left to delete.
    return false;
  }
}

/**@brief   Process a delete all command.
 *
 * Delete records, one by one, until no records are left.
 */
void run_fds_delete_all_files_process(void) {
  if (m_delete_all.delete_next & !m_delete_all.pending) {
    NRF_LOG_INFO("Deleting next record.");

    m_delete_all.delete_next = record_delete_next();
    if (!m_delete_all.delete_next) {
      NRF_LOG_INFO("No records left to delete.");
    }
  }
}

#ifdef __cplusplus
}
#endif  // __cplusplus

AppMainBoard app_main_board;

constexpr bool AppMainBoard::READ_REQUEST_ACTIVE_STATE;

void AppMainBoard::init() {
  enableBleIntPin(!READ_REQUEST_ACTIVE_STATE);
  setSpiBuffers();

#if defined(SW_VERSION_MAJOR) && defined(SW_VERSION_MINOR) && defined(SW_VERSION_PATCH)
  // param_dfu_metadata.getMessage().software_version.major = SW_VERSION_MAJOR;
  // param_dfu_metadata.getMessage().software_version.minor = SW_VERSION_MINOR;
  // param_dfu_metadata.getMessage().software_version.patch = SW_VERSION_PATCH;
#endif  // SW_VERSION_MAJOR
  // param_dfu_metadata.getMessage().has_software_version = true;
  NRF_LOG_INFO("Software version: %d.%d.%d", SW_VERSION_MAJOR, SW_VERSION_MINOR, SW_VERSION_PATCH);
  NRF_LOG_FLUSH();

  // #ifdef USE_FDS
  //   InitFds();
  // #endif  // USE_FDS
}

void AppMainBoard::InitFds() {
  ret_code_t err_code;
  // Register first to receive an event when initialization is complete.
  (void)fds_register(fds_evt_handler);
  NRF_LOG_INFO("Initializing fds...");
  err_code = fds_init();
  APP_ERROR_CHECK(err_code);
  // Wait for fds to initialize.
  wait_for_fds_ready();

  fds_stat_t stat = { 0 };
  err_code = fds_stat(&stat);
  APP_ERROR_CHECK(err_code);

  NRF_LOG_INFO("Found %d valid records.", stat.valid_records);
  NRF_LOG_INFO("Found %d dirty records (ready to be garbage collected).", stat.dirty_records);

  // run garbage collection?
  if (stat.dirty_records > 0) {
    NRF_LOG_INFO("Running garbage collection...");
    err_code = fds_gc();
    APP_ERROR_CHECK(err_code);
  }

  // RunFdsGarbageCollection();

  if (true) {
    fds_record_desc_t desc = { 0 };
    fds_find_token_t tok = { 0 };

    err_code = fds_record_find(CONFIG_FILE, CONFIG_REC_KEY, &desc, &tok);

    if (err_code == NRF_SUCCESS) {
      // if (false) {
      /* A config file is in flash. Let's update it. */
      fds_flash_record_t config = { 0 };
      /* Open the record and read its contents. */
      err_code = fds_record_open(&desc, &config);
      APP_ERROR_CHECK(err_code);
      /* Copy the configuration from flash into yr_fds_config_struct. */
      memcpy(&yr_fds_config_struct, config.p_data, sizeof(yr_fds_config_struct_t));
      NRF_LOG_INFO("Config file found, updating boot count to %d.", yr_fds_config_struct.boot_count);
      NRF_LOG_INFO("Config file found, main_board serial_number: [%s].", yr_fds_config_struct.main_board_serial_number);
      /* Update boot count. */
      yr_fds_config_struct.boot_count++;
      fds_config_found_ = true;
      /* Close the record when done reading. */
      err_code = fds_record_close(&desc);
      APP_ERROR_CHECK(err_code);

      /* Write the updated record to flash. */
      // err_code = fds_record_update(&desc, &m_dummy_fds_record);
      // if ((err_code != NRF_SUCCESS) && (err_code == FDS_ERR_NO_SPACE_IN_FLASH)) {
      //   NRF_LOG_INFO("No space in flash, delete some records to update the config file.");
      // } else {
      //   APP_ERROR_CHECK(err_code);
      // }
    } else {
      /* System config not found; write a new one. */
      NRF_LOG_INFO("Writing config file...");
      memset(yr_fds_config_struct.main_board_serial_number, '\0',
             sizeof(yr_fds_config_struct.main_board_serial_number));
      strncpy(yr_fds_config_struct.main_board_serial_number, "UNSET",
              sizeof(yr_fds_config_struct.main_board_serial_number));

      // ĺĺ§ĺéĺŻščŽžĺ¤ĺşĺĺˇä¸şć ćďż˝0xff)
      memset(yr_fds_config_struct.paired_device_serial_number, 0xFF,
             sizeof(yr_fds_config_struct.paired_device_serial_number));
      err_code = fds_record_write(&desc, &m_dummy_fds_record);
      if ((err_code != NRF_SUCCESS) && (err_code == FDS_ERR_NO_SPACE_IN_FLASH)) {
        NRF_LOG_INFO("No space in flash, delete some records to update the config file.");
      } else {
        NRF_LOG_INFO("Wrote initial config file.\r\n");
        NRF_LOG_FLUSH();
        APP_ERROR_CHECK(err_code);
      }
    }
  }
  ReadSerialNumberFromFlash();
  ReadPairedDeviceSerialNumberFromFlash();
}

void AppMainBoard::RunFdsGarbageCollection() {
  delete_all_begin();
  const size_t kNumIterations = 2000;
  for (int i = 0; i < kNumIterations; i++) {
    run_fds_delete_all_files_process();
    // fds_gc();
    if (i % 1000 == 0) {
      NRF_LOG_INFO("AppMainBoard::RunFdsGarbageCollection() - fds_gc() iteration %d", i);
    }
    NRF_LOG_FLUSH();
  }
}

//----------------------------------------------------------------------------//
//  FDS Flash Data Storage - Read/Write
//----------------------------------------------------------------------------//
void AppMainBoard::ReadSerialNumberFromFlash() {
  uint32_t err_code;
  fds_record_desc_t desc = { 0 };
  fds_find_token_t tok = { 0 };
  err_code = fds_record_find(CONFIG_FILE, CONFIG_REC_KEY, &desc, &tok);
  NRF_LOG_INFO("Reading Serial Number from Flash.");
  if (err_code == NRF_SUCCESS) {
    fds_flash_record_t config = { 0 };
    err_code = fds_record_open(&desc, &config);
    APP_ERROR_CHECK(err_code);
    memcpy(&yr_fds_config_struct, config.p_data, sizeof(yr_fds_config_struct_t));
    NRF_LOG_INFO("Config file found, main_board serial_number: [%s].", yr_fds_config_struct.main_board_serial_number);
    strncpy(serial_number_str_, yr_fds_config_struct.main_board_serial_number, sizeof(serial_number_str_));
    err_code = fds_record_close(&desc);
    APP_ERROR_CHECK(err_code);
  } else {
    NRF_LOG_INFO("Config file not found, main_board serial_number: [%s].",
                 yr_fds_config_struct.main_board_serial_number);
    // strncpy(serial_number_str_, "UNSET", sizeof(serial_number_str_));
  }
}

void AppMainBoard::WriteSerialNumberToFlash(char *serial_number_str, const size_t len) {
  uint32_t err_code;
  fds_record_desc_t desc = { 0 };
  fds_find_token_t tok = { 0 };
  err_code = fds_record_find(CONFIG_FILE, CONFIG_REC_KEY, &desc, &tok);
  NRF_LOG_INFO("Writing Serial Number to Flash.");
  // memset(yr_fds_config_struct.main_board_serial_number, '\0', sizeof(yr_fds_config_struct.main_board_serial_number));
  strncpy(yr_fds_config_struct.main_board_serial_number, serial_number_str, len);

  if (err_code == NRF_SUCCESS) {
    // Write the updated record to flash.
    err_code = fds_record_update(&desc, &m_dummy_fds_record);
    if ((err_code != NRF_SUCCESS) && (err_code == FDS_ERR_NO_SPACE_IN_FLASH)) {
      NRF_LOG_INFO("No space in flash, delete some records to update the config file.");
    } else {
      APP_ERROR_CHECK(err_code);
    }
  }
  // err_code = fds_record_write(&desc, &m_dummy_fds_record);
  // if ((err_code != NRF_SUCCESS) && (err_code == FDS_ERR_NO_SPACE_IN_FLASH)) {
  //   NRF_LOG_INFO("No space in flash, delete some records to update the config file.");
  // } else {
  //   APP_ERROR_CHECK(err_code);
  // }

  NRF_LOG_INFO(
      "\r\n\r\nAppMainBoard::WriteSerialNumberToFlash(): Reset the Main Board for update the BLE Advertising "
      "name.\r\n\r\n");
  NRF_LOG_FLUSH();
}

//----------------------------------------------------------------------------//
//
//----------------------------------------------------------------------------//
void AppMainBoard::SendConnectionState(const connection_state_t &connection_state) {
  NRF_LOG_INFO("Sending connection state to target.");
  uint8_t buffer[10];
  buffer[0] = 61;  // @TODO(ks): YrHeader key for NRF -> MB Comm
  buffer[1] = 2;
  buffer[2] = 0;  // CRC unused
  buffer[3] = 1;  // command-id: "Connection State Change" command tentatively = 1
  buffer[4] = connection_state;
  uint8_t total_packet_len = buffer[1] + 3;  // 3-byte header + datalen stored in buffer[1]
  addToSpiTxBuffer(buffer, total_packet_len);
}

//----------------------------------------------------------------------------//
//  PIN OPERATIONS
//----------------------------------------------------------------------------//
void AppMainBoard::resetK66Pin() {
  NRF_LOG_INFO("Resetting K66 ...");
#ifdef USE_MOCK_TARGET
  mock_target_.reset();
#else
  nrf_gpio_pin_write(PIN_RESET_K66, 1);
  for (int i = 0; i < 10; i++) {
    nrf_gpio_pin_write(PIN_RESET_K66, 0);
    // delay @TODO(ks)
    for (int j = 0; j < 10000; j++) {
      int a = 10;
      a /= 2;
    }
  }
  nrf_gpio_pin_write(PIN_RESET_K66, 1);
#endif  // USE_MOCK_TARGET
  NRF_LOG_INFO("Reset K66 Done");
}


//----------------------------------------------------------------------------//
//  SPI RX
//----------------------------------------------------------------------------//
// void AppMainBoard::handleSpiRx() {
void AppMainBoard::handleSpiRx(const size_t &len) {
  const size_t data_len = getSpiPacketDataLength(spi_rx_buffer, sizeof(spi_rx_buffer));
  // const size_t total_len = getSpiPacketTotalLength(spi_rx_buffer, sizeof(spi_rx_buffer));

#ifdef DEBUG_SPI_RX
  if (data_len > 0) {
    NRF_LOG_INFO(">>   App::handleSpiRxMsg() SPI RX Len: [%d]", len);
  }
#endif  // DEBUG_SPI_RX

  if (data_len != 0) {
    uint32_t dt_comm_request = millis() - time_last_received_comm_request_;
    if (dt_comm_request > timeout_comm_request_) {
      /*
        uint8_t seq_no = spi_rx_buffer[2];

        if(seq_no_set_){
          if (seq_no != (uint8_t)(last_seq_no_+1)){
            msg_stats[SPI_MISSING_SEQ_NOS].add();
          }
        }else{
          seq_no_set_ = true;
        }
        last_seq_no_ = seq_no;
      */
      addToSpiRxBuffer(spi_rx_buffer, len);
    }
  }

#ifdef DEBUG_DFU_SPI_TIMER
  if (spi_debug_timers_[spi_debug_timer_t::SPI_DBG_TX] != 0) {
    if (spi_debug_timers_[spi_debug_timer_t::SPI_DBG_RX_0] == 0) {
      spi_debug_timers_[spi_debug_timer_t::SPI_DBG_RX_0] = millis() - spi_debug_timers_[spi_debug_timer_t::SPI_DBG_TX];
    } else {
      if (spi_debug_timers_[spi_debug_timer_t::SPI_DBG_RX_1] == 0) {
        spi_debug_timers_[spi_debug_timer_t::SPI_DBG_RX_1] =
            millis() - spi_debug_timers_[spi_debug_timer_t::SPI_DBG_TX];
      } else {
        if (spi_debug_timers_[spi_debug_timer_t::SPI_DBG_RX_2] == 0) {
          spi_debug_timers_[spi_debug_timer_t::SPI_DBG_RX_2] =
              millis() - spi_debug_timers_[spi_debug_timer_t::SPI_DBG_TX];
        }
      }
    }
  }
#endif  // DEBUG_DFU_SPI_TIMER

  // #ifndef USE_BLHOST
  //     memset(spi_tx_buffer, 0, sizeof(spi_tx_buffer));
  //     for (size_t i = 0; i < sizeof(spi_rx_buffer); i++) {
  //       ble_tx_buffer[i] = spi_rx_buffer[i];
  //     }
  // #endif  // USE_BLHOST

  // if (dfu_manager_.dfu_state_ == SEND_DATA) {
  //   NRF_LOG_INFO("[%lu] spis_event_handler", millis());
  // }

  // if (spi_rx_buffer[0] != 0) {
  // print_buffer_len(spi_rx_buffer, 4, ">>   App::handleSpiRxMsg()");

  msg_stats[SPI_RX].add();
  time_last_received_spi_msg_ = millis();

  // spi_state.setVal(SPI_RECEIVED);
  // spi_receive_state_.setVal(STATE_RECEIVED);

  // addToSpiRxBuffer(spi_rx_buffer, SPI_BUF_SIZE);

  // clearSpiTxBuffer();
  // clearSpiRxBuffer();

  if (spi_tx_state_.getVal() == spi_tx_state_t::SPI_TX_SET) {
    uint32_t dt_sent_spi_ms = millis() - time_last_sent_spi_msg_;
    // if (dt_sent_spi_ms < 3) {
    // } else {
    if (!spi_tx_packet_ringbuf.isEmpty()) {
      spi_packet_t tmp;
      spi_tx_packet_ringbuf.pull(&tmp);
#ifdef DEBUG_DFU_SPI_TX
      NRF_LOG_INFO(">>   App::handleSpiRxMsg() SPI TX - empty packet success [%d] [dt=%d ms]",
                   spi_tx_packet_ringbuf.numElements(), dt_sent_spi_ms);
#endif  // DEBUG_DFU_SPI_TX
      spi_tx_state_.setVal(spi_tx_state_t::SPI_TX_SENT);
      clearSpiTxBuffer();
    }
    //
  }

#ifdef USE_READ_REQUEST
  if (!dfu_manager_.isActive()) {
    // pull low during interrupt
    enableBleIntPin(!READ_REQUEST_ACTIVE_STATE);
//     if (read_request_status_.getVal() == READ_REQUEST_SET) {
//       enableBleIntPin(!READ_REQUEST_ACTIVE_STATE);
//       read_request_status_.setVal(READ_REQUEST_COMPLETE);
#ifdef DEBUG_READ_REQUEST
    NRF_LOG_INFO("              Read Request Status COMPLETE, pull pin low, num: [%ld]\r\n",
                 spi_tx_packet_ringbuf.numElements());
#endif  // DEBUG_READ_REQUEST
    //     }
  }
#endif  // USE_READ_REQUEST

  if (!dfu_manager_.isActive()) {
    if (!spi_tx_packet_ringbuf.isEmpty()) {
      bool need_process_spi_tx = emptySpiTxBuffer();
      if (need_process_spi_tx) {
#ifdef DEBUG_READ_REQUEST
        NRF_LOG_INFO("              Emptied SPI Tx: Num: [%ld]\r\n", spi_tx_packet_ringbuf.numElements());
        // printByteArrayWidth(spi_tx_buffer, sizeof(spi_tx_buffer), 25);
#endif  // DEBUG_READ_REQUEST
        time_last_sent_spi_msg_ = millis();
      }
    }
  }

  // bool changed = spi_tx_state_.changed();
  // if (changed) {
  //   // NRF_LOG_INFO(">>     App::handleSpiRxMsg() SPI TX STATE CHANGED [%d]", spi_tx_state_.getVal());
  // }
  // spi_tx_state_.update();

  clearSpiRxBuffer();

  spi_buffers_set_ = false;
  setSpiBuffers();
}

void AppMainBoard::handleSpiRxMsg(uint8_t *buf, const size_t &len) {
  // print_buffer_len(buf, len, ">>   handleSpiRxMsg()");
  memcpy(spi_rx_buffer, buf, len);
  print_buffer_len(spi_rx_buffer, len, ">>   App::handleSpiRxMsg()");
  handleSpiRx(len);
}

bool AppMainBoard::addToSpiRxBuffer(uint8_t *buf, const size_t &len) {
  spi_packet_t packet;
  packet.length = len;
  // memset(packet.data, 0, sizeof(packet.data));
  memcpy(packet.data, buf, len);
  spi_rx_packet_ringbuf.add(packet);
#ifdef DEBUG_SPI_RX
  NRF_LOG_INFO("   App::addToSpiRxBuffer - add packet [%d]", spi_rx_packet_ringbuf.numElements());
#endif  // DEBUG_SPI_RX
  return true;
}

bool AppMainBoard::emptySpiRxBuffer() {
  if (spi_rx_packet_ringbuf.isEmpty()) {
    return false;
  }
#ifdef DEBUG_SPI_RX
  NRF_LOG_INFO("   App::emptySpiRxBuffer - empty packet [%d]", spi_rx_packet_ringbuf.numElements());
#endif  // DEBUG_SPI_RX
  spi_packet_t packet;
  spi_rx_packet_ringbuf.pull(&packet);
  // memset(spi_rx_process_buffer, 0, sizeof(spi_rx_process_buffer));
  memcpy(spi_rx_process_buffer, packet.data, packet.length);
  return true;
}

//----------------------------------------------------------------------------//
//  SPI TX
//----------------------------------------------------------------------------//
void AppMainBoard::writeSpiDataTxBootloader(uint8_t *buf, const size_t &len) {
  size_t total_len = len;
#ifdef USE_NEW_SPI_PROTOCOL
  total_len = len + 3;
  spi_tx_process_buffer[0] = 0;  // id
  spi_tx_process_buffer[1] = len;
  spi_tx_process_buffer[2] = 0;  // crc
  memcpy(&spi_tx_process_buffer[3], buf, len);
#else
  total_len = len + 1;
  spi_tx_process_buffer[0] = len;
  memcpy(&spi_tx_process_buffer[1], buf, len);
#endif  // USE_NEW_SPI_PROTOCOL
#ifdef DEBUG_DFU_SPI_TX
  print_buffer_len(spi_tx_process_buffer, total_len, "<<   App::writeSpiDataTxBootloader");
#endif  // DEBUG_DFU_SPI_TX
#ifdef USE_MOCK_TARGET
  // spi_tx_state_.setVal(STATE_RECEIVED);
//   mock_target_.onReceivePacket(buffer, len);
#else
  // setSpiBuffers();
  // toggleSpiInterrupt();
  addToSpiTxBuffer(spi_tx_process_buffer, total_len);
#endif  // USE_MOCK_TARGET
}

bool AppMainBoard::addToSpiTxBuffer(uint8_t *buf, const size_t &len) {
  spi_packet_t packet;
  packet.length = len;
  // memset(packet.data, 0, sizeof(packet.data));
  memcpy(packet.data, buf, len);
  spi_tx_packet_ringbuf.add(packet);
  // NRF_LOG_INFO("App::addToSpiTxBuffer - add packet [%d] [%d] [%d] [%d]
  // [%d]",packet.data[0],packet.data[1],packet.data[2],packet.data[3],packet.data[4]);
#ifdef DEBUG_DFU_SPI_TX
  NRF_LOG_INFO("   App::addToSpiTxBuffer - add packet [%d]", spi_tx_packet_ringbuf.numElements());
#endif  // DEBUG_DFU_SPI_TX

  return true;
}

bool AppMainBoard::emptySpiTxBuffer() {
  if (spi_tx_packet_ringbuf.isEmpty()) {
    return false;
  }
  if (spi_tx_state_.getVal() == spi_tx_state_t::SPI_TX_SET) {
    return false;
  }
  // if (spi_buffers_set_) {
  //   return;
  // }
  spi_packet_t *packet_ptr = spi_tx_packet_ringbuf.peek(0);
#ifdef DEBUG_DFU_SPI_TX
  NRF_LOG_INFO("   App::emptySpiTxBuffer - empty packet [%d]", spi_tx_packet_ringbuf.numElements());
#endif  // DEBUG_DFU_SPI_TX

  // clearSpiTxBuffer();
  memcpy(spi_tx_buffer, packet_ptr->data, packet_ptr->length);

  // spi_packet_t packet;
  // spi_tx_packet_ringbuf.pull(&packet);
  // memset(spi_tx_buffer, 0, sizeof(spi_tx_buffer));
  // memcpy(spi_tx_buffer, packet.data, packet.length);

  // spi_buffers_set_ = false;
  uint32_t ret_code = 2;
  // uint32_t ret_code = setSpiBuffers(true);

  // if (ret_code != 2) {
  //   return false;
  // }

  // if (spi_buffers_set_) {
  if (ret_code == 2) {
    spi_tx_state_.setVal(spi_tx_state_t::SPI_TX_SET);
    return true;
  } else {
    // clearSpiTxBuffer();
  }

  return false;
}

uint32_t AppMainBoard::setSpiBuffers(const bool &tx) {
#ifdef USE_MOCK_TARGET
  return true;
#endif  // USE_MOCK_TARGET

  // if (spi_buffers_set_) {
  //   return 0;
  // }

  // if (spi_tx_buffer[0] != 0) {
  //   spi_state.setVal(SPI_SET);
  //   time_last_spi_buffers_set_ = millis();
  // }

  // memset(spi_rx_buffer, 0, sizeof(spi_rx_buffer));

  uint32_t err_code;
  // APP_ERROR_CHECK(nrf_drv_spis_buffers_set(&spis, spi_tx_buffer, SPI_BUF_SIZE, spi_rx_buffer, SPI_BUF_SIZE));
  err_code = nrf_drv_spis_buffers_set(&spis, spi_tx_buffer, SPI_BUF_SIZE, spi_rx_buffer, SPI_BUF_SIZE);
  // if (NRF_ERROR_INVALID_STATE == err_code) {
  if (NRF_SUCCESS != err_code) {
    NRF_LOG_INFO("\r\nSPI ERROR [%d]\r\n\r\n", err_code);
    return 1;
  }

#ifdef DEBUG_DFU_SPI_TX
  if (tx) {
    NRF_LOG_INFO("  <App::setSpiBuffers: [%s]", tx ? "TX" : "RX");
  }
#endif  // DEBUG_DFU_SPI_TX

  spi_buffers_set_ = true;
  return 2;
}

//----------------------------------------------------------------------------//
//  SPI UTIL
//----------------------------------------------------------------------------//
bool AppMainBoard::setUseTxInterrupt(const bool &state) {
  using_tx_interrupt_ = state;
  NRF_LOG_INFO("   App::setUseTxInterrupt [%d]", using_tx_interrupt_ ? 1 : 0);
}

void AppMainBoard::enableBleIntPin(const bool &enable) {
  const int state = enable ? 1 : 0;
  nrf_gpio_pin_write(PIN_SPI_WRITE_INT, state);
}

void AppMainBoard::toggleSpiInterrupt() {
#ifdef DEBUG_DFU_SPI_BUFFER
  NRF_LOG_INFO("<<   App::toggleSpiInterrupt")
#endif  // DEBUG_DFU_SPI_BUFFER
  time_last_toggle_interrupt_ = millis();
  // enableBleIntPin(false);
  // enableBleIntPin(true);
  enableBleIntPin(READ_REQUEST_ACTIVE_STATE);
  enableBleIntPin(!READ_REQUEST_ACTIVE_STATE);
  toggle_spi_interrupt_count_++;
}

bool AppMainBoard::isEmptyFrame() {
  for (uint32_t i = 0; i < sizeof(spi_rx_buffer) - 2; i++) {
    uint8_t b0 = spi_rx_buffer[i];
    uint8_t b1 = spi_rx_buffer[i + 1];
    uint8_t b2 = spi_rx_buffer[i + 2];
    if ((b0 == 1) && (b1 == 2) && (b2 == 3)) {
      return true;
    }
  }
  return false;
}

void AppMainBoard::printSpiBuffers() {
  //  if (!isEmptyFrame()) {
  // print_buffer(spi_rx_buffer, sizeof(spi_rx_buffer), " - Rx");
  print_buffer(spi_rx_buffer, 10, " - Rx");
  //  if (!areBothBuffersSame(spi_tx_buffer, spi_tx_buffer_last, sizeof(spi_tx_buffer))) {
  // print_buffer(spi_tx_buffer, sizeof(spi_tx_buffer), " - Tx");
}

//----------------------------------------------------------------------------//
//  BLE RX
//----------------------------------------------------------------------------//
void AppMainBoard::handleBleRx(uint8_t *buf, const size_t &len) {
  msg_stats[BLE_RX].add();
  ble_receive_state_.setVal(STATE_RECEIVED);
  // time_last_rx_ble = millis();
#ifdef DEBUG_DFU_BLE_RX
  NRF_LOG_INFO("   App::handleBleRx - datalen: [%d] ble_rx_count: [%d]", len, msg_stats[BLE_RX].getCount());
  print_buffer_len(buf, 6, "   App::handleBleRx data");
#endif  // DEBUG_DFU_BLE_RX
  addToBleRxBuffer(buf, len);
}

bool AppMainBoard::addToBleRxBuffer(uint8_t *buf, const size_t &len) {
  // size_t max_length = min(sizeof(ble_rx_buffer), max_length);
  ble_packet_t packet;
  packet.length = len;
  memset(packet.data, 0, sizeof(packet.data));
  // memcpy(packet.data, buf, max_length);
  memcpy(packet.data, buf, len);
  ble_rx_packet_ringbuf.add(packet);
#ifdef DEBUG_DFU_BLE_RX
  NRF_LOG_INFO("   App::addToBleRxBuffer - add packet [%d]", ble_rx_packet_ringbuf.numElements());
#endif  // DEBUG_DFU_BLE_RX
  return true;
}

bool AppMainBoard::emptyBleRxBuffer() {
  if (ble_rx_packet_ringbuf.isEmpty()) {
    return false;
  }
#ifdef DEBUG_DFU_BLE_RX
  NRF_LOG_INFO("   App::emptyBleRxBuffer - empty packet [%d]", ble_rx_packet_ringbuf.numElements());
#endif  // DEBUG_DFU_BLE_RX
  ble_packet_t packet;
  ble_rx_packet_ringbuf.pull(&packet);
  memset(ble_rx_process_buffer, 0, sizeof(ble_rx_process_buffer));
  memcpy(ble_rx_process_buffer, packet.data, packet.length);
  return true;
}

void AppMainBoard::processBleRxMsg() {
  uint8_t rx_key = ble_rx_process_buffer[0];

  if (rx_key == 0 || rx_key == 0xff) {
    return;
  }

  // print_buffer_len(ble_rx_process_buffer, sizeof(ble_rx_process_buffer), "BLE Rx");
  // return;

  switch (rx_key) {
    //----------------------------------------------------------------------------//
    //  RESTART
    //----------------------------------------------------------------------------//
    case TransmissionPacket_Type_KEY_RESTART: {
      NRF_LOG_INFO("\r\nReceived RESTART: [%02X]\r\n", rx_key);
      resetK66Pin();
    } break;

    //----------------------------------------------------------------------------//
    //  DFU COMMANDS
    //----------------------------------------------------------------------------//
    case Dfu_Keys_KEY_DFU_COMMAND: {
      size_t INDEX_DFU_CMD_ID = 3;
      size_t INDEX_DFU_CMD_DATA = 4;

      uint8_t cmd = ble_rx_process_buffer[INDEX_DFU_CMD_ID];
#ifdef DEBUG_DFU_BLE_RX
      NRF_LOG_INFO("Received DFU Command: [%02X]", cmd);
#endif  // DEBUG_DFU_BLE_RX
      switch (cmd) {
        case Dfu_Cmd_KEY_DFU_PACKET_RESPONSE: {
#ifdef DEBUG_DFU_BLE_RX
          NRF_LOG_INFO("Got DFU_PACKET_RESPONSE");
#endif  // DEBUG_DFU_BLE_RX
          dfu_manager_.parseFirmwarePacket(&ble_rx_process_buffer[INDEX_DFU_CMD_DATA],
                                           sizeof(ble_rx_process_buffer) - INDEX_DFU_CMD_DATA);
#ifdef USE_DFU_TEST_COMM
          dfu_manager_.checkIfNeedRequestNewPacketFromClient();
          // dfu_manager_.emptyRingBufRaw();
#endif  //  USE_DFU_TEST_COMM
        } break;
        case Dfu_Cmd_KEY_DFU_METADATA_REQUEST: {
          NRF_LOG_INFO("Got DFU_METADATA_REQUEST");
          // dfu_manager_.sendDfuMetadataResponse();
          NRF_LOG_INFO("Sent DFU_METADATA_RESPONSE");
        } break;
        case Dfu_Cmd_KEY_DFU_ERASE_NEW_APP: {
          NRF_LOG_INFO("Got DFU_ERASE_NEW_APP");
          dfu_manager_.opt_only_erase_new_app_ = true;
          dfu_manager_.sendDfuMetadataResponse();
        } break;
        case Dfu_Cmd_KEY_DFU_START: {
          NRF_LOG_INFO("Got DFU Start");
#ifdef USE_DFU_TEST_COMM
          // dfu_manager_.resetDfuProgressAndBuffers();
          dfu_manager_.sendDfuPacketRequest(0);

#else
          dfu_manager_.parseFirmwarePacketMeta(&ble_rx_process_buffer[INDEX_DFU_CMD_DATA],
                                               sizeof(ble_rx_process_buffer) - INDEX_DFU_CMD_DATA);
          // break;
          // dfu_manager_.sendDfuPacketRequest(0);
          // break;
          dfu_manager_.resetK66AndEnterDfuStateMachine();
          dfu_manager_.setActive(true);
          // setUseTxInterrupt(true);
          // NRF_LOG_FLUSH();
#endif  //  USE_DFU_TEST_COMM
        } break;
        case Dfu_Cmd_KEY_DFU_CANCEL: {
          NRF_LOG_INFO("Got DFU Cancel");
          dfu_manager_.setActive(false);
          setUseTxInterrupt(false);
        } break;
      }
    } break;

    //----------------------------------------------------------------------------//
    //  BLE COMMANDS
    //----------------------------------------------------------------------------//
    case TransmissionPacket_Type_KEY_BLE_COMMAND: {
      size_t INDEX_BLE_CMD_ID = 3;
      size_t INDEX_BLE_CMD_DATA = 4;
      uint8_t cmd = ble_rx_process_buffer[INDEX_BLE_CMD_ID];
#ifdef DEBUG_BLE_CMD_RX
      NRF_LOG_INFO("Received BLE COMMAND [%02X]", cmd);
#endif  // DEBUG_BLE_CMD_RX
      switch (cmd) {
        case BleCmd_Cmd_KEY_COMM_TEST_REQUEST: {
#ifdef DEBUG_BLE_CMD_RX
          NRF_LOG_INFO("Received BLE COMM TEST REQUEST");
          clearBleTxRingBuffer("App::processBleRxMsg() BLE COMM TEST - Clear Buffer");
          time_last_received_comm_request_ = millis();
          const uint8_t num_packets_to_send = 10;
          const uint8_t packet_size = 200;
          const uint8_t header_ext_size = 4;
          const uint8_t total_len = packet_size + header_ext_size;
          memset(spi_rx_process_buffer, 0, sizeof(spi_rx_process_buffer));
          spi_rx_process_buffer[0] = TransmissionPacket_Type_KEY_BLE_COMMAND;
          spi_rx_process_buffer[1] = packet_size + header_ext_size;
          spi_rx_process_buffer[2] = 1;  // @TODO(ks): CRC fake
          spi_rx_process_buffer[3] = BleCmd_Cmd_KEY_COMM_TEST_RESPONSE;
          memset(&spi_rx_process_buffer[header_ext_size], 123, packet_size);
          for (size_t i = 0; i < num_packets_to_send; i++) {
            bool sent = sendBluetoothMsgBuffer(spi_rx_process_buffer, total_len);
          }
#endif  // DEBUG_BLE_CMD_RX
        } break;
      }

    } break;
    default: {
      if (!dfu_manager_.isActive()) {
#ifdef DEBUG_DFU_BLE_RX
        NRF_LOG_INFO("   App::processBleRxMsg - addToSpiTxBuffer [%d]", spi_tx_packet_ringbuf.numElements());
#endif  // DEBUG_DFU_BLE_RX
        addToSpiTxBuffer(ble_rx_process_buffer, sizeof(ble_rx_process_buffer));
      }
      // memcpy(spi_tx_buffer, ble_rx_process_buffer, sizeof(spi_tx_buffer));  //
      // setSpiBuffers();
    } break;
  }

  // clearSpiTxBuffer();
  // clearBleRxBuffer();
  clearBleRxProcessBuffer();
}

//----------------------------------------------------------------------------//
//  BLE TX
//----------------------------------------------------------------------------//
bool AppMainBoard::sendBluetoothMsgDirect(uint8_t *buf, const size_t &len) {
  size_t max_len = min(len, sizeof(ble_tx_buffer));
  memcpy(ble_tx_buffer, buf, max_len);
  return sendBluetoothMsgDirectLen(max_len);
}

bool AppMainBoard::sendBluetoothMsgDirectLen(const size_t &length_in) {
  uint32_t err_code;

  if (last_tx_ble_failed_) {
    uint32_t dt_attempt_ms = millis() - time_last_tx_ble_;
    if (dt_attempt_ms < 1) {
      return false;
    }
  }

  // time_last_tx_ble_ = millis();

  msg_stats[BLE_TX_ATTEMPT].add();

  if (!is_ble_connected_.getVal()) {
    return true;
  }

  // if (ble_tx_buffer[0] == 0 || ble_tx_buffer[0] == 0xff) {
  //   return true;
  // }

  // bool use_delay_before_send = true;
  // uint32_t dt_ms = millis() - is_ble_connected_.getTime();
  // if (use_delay_before_send) {
  //   if (dt_ms < 500) {
  //     return true;
  //   }
  // }

  uint16_t length = min(length_in, sizeof(ble_tx_buffer));
  err_code = sendBluetoothMsgProxy(ble_tx_buffer, length);
  bool success = (NRF_SUCCESS == err_code);
  last_tx_ble_failed_ = !success;

  // only save to buffer if tx is ready
  if (is_ble_connected_.getVal() && !is_ble_tx_ready_.getVal()) {
    if (success) {
      is_ble_tx_ready_.setVal(true);
      uint32_t dt_connection_delay_ms = is_ble_tx_ready_.getTime() - is_ble_connected_.getTime();
      NRF_LOG_INFO("Connection delay [%d ms]", dt_connection_delay_ms);
      clearBleTxRingBuffer("App::sendBluetoothMsgDirectLen() BLE Tx Ready");
    } else {
      // uint32_t dt_connection_delay_ms = millis() - is_ble_connected_.getTime();
      // NRF_LOG_INFO("Discard message [%d ms]", dt_connection_delay_ms);
      return true;
    }
  }

  if ((err_code != NRF_ERROR_INVALID_STATE) && (err_code != NRF_ERROR_RESOURCES) && (err_code != NRF_ERROR_NOT_FOUND)) {
  }
  if (err_code == NRF_ERROR_RESOURCES) {
  }
  if (err_code != 0) {
    time_last_tx_ble_ = millis();
#ifdef DEBUG_BLE_TX_ERRORS
    if (msg_stats[BLE_TX].getCount() % 50 == 0) {
      uint32_t dt_ms = millis() - is_ble_connected_.getTime();
      NRF_LOG_INFO("sendBluetoothMsgDirect [%d] dt: [%d] error [%d]", is_ble_connected_.getVal() ? 1 : 0, dt_ms,
                   err_code);
    }
#endif  // DEBUG_BLE_TX_ERRORS
  }

  if (success) {
    msg_stats[BLE_TX].add();
    memset(ble_tx_buffer, 0, sizeof(ble_tx_buffer));  // @TODO check
  }

  return success;
}

bool AppMainBoard::sendBluetoothMsgBuffer(uint8_t *buf, const size_t &len) {
#ifdef USE_BLE_TX_RING_BUFFER
  //  bool sent = sendBluetoothMsgDirect(buf, len);
  bool sent = false;
  if (sent) {
    return true;
  } else {
    ble_packet_t packet;
    packet.length = len;

    memset(packet.data, 0, sizeof(packet.data));
    memcpy(packet.data, buf, len);

    if (!ble_tx_packet_ringbuf.add(packet)) {
      msg_stats[BLE_TX_BUF_OFLOWED].add();
    }

    ;
#ifdef DEBUG_DFU_BLE_TX
    NRF_LOG_INFO("txbuf - add packet [%d] Len: [%d]", ble_tx_packet_ringbuf.numElements(), len);
#endif  // DEBUG_DFU_BLE_TX
  }
#endif  // USE_BLE_TX_RING_BUFFER
  return false;
}

void AppMainBoard::clearBleTxRingBuffer(const char *msg) {
  NRF_LOG_INFO("[%s] - clear BLE Tx Buffer [%d]", msg, ble_tx_packet_ringbuf.numElements());
  ble_tx_packet_ringbuf.clear();
  msg_stats[BLE_TX_BUF_OFLOWED].reset();
  msg_stats[SPI_MISSING_SEQ_NOS].reset();
  msg_stats[BLE_TX_COMPLETED].reset();
  msg_stats[BLE_TX].reset();
}

//----------------------------------------------------------------------------//
//  BLE RX
//----------------------------------------------------------------------------//
void AppMainBoard::handleConnected() {
  is_ble_connected_.setVal(true);
  clearBleTxRingBuffer("App::handleConnected");
  SendConnectionState(CONNECTED);
}

void AppMainBoard::handleDisconnected() {
  is_ble_connected_.setVal(false);
  is_ble_tx_ready_.setVal(false);
  clearBleTxRingBuffer("App::handleDisconnected");
  SendConnectionState(DISCONNECTED);
}

void AppMainBoard::processSpiRxMsg(uint8_t *buf, const size_t &len) {
  memcpy(spi_rx_process_buffer, buf, len);
  processSpiRxMsg();
}

size_t AppMainBoard::getSpiPacketDataLength(uint8_t *buf, const size_t &len) {
#ifdef USE_NEW_SPI_PROTOCOL
  // uint8_t hi = buf[1];
  // uint8_t lo = buf[3];
  // return hi << 8 | lo;
  return buf[1];
#else
  return buf[0];
#endif  // USE_NEW_SPI_PROTOCOL
}

size_t AppMainBoard::getSpiPacketTotalLength(uint8_t *buf, const size_t &len) {
#ifdef USE_NEW_SPI_PROTOCOL
  return buf[1] + 3;
#else
  return buf[0];
#endif  // USE_NEW_SPI_PROTOCOL
}

//----------------------------------------------------------------------------//
//  PROCESS SPI
//----------------------------------------------------------------------------//
void AppMainBoard::processSpiRxMsg() {
  spi_receive_state_.setVal(STATE_PROCESSED);

  // const bool valid_spi_data = spi_rx_process_buffer[0] != 0;
  // const uint8_t data_len = spi_rx_process_buffer[0] + 1;

  const size_t data_len = getSpiPacketDataLength(spi_rx_process_buffer, sizeof(spi_rx_process_buffer));
  const size_t total_len = getSpiPacketTotalLength(spi_rx_process_buffer, sizeof(spi_rx_process_buffer));
  const bool valid_spi_data = data_len > 0;


#ifdef DEBUG_DFU_BUFFER
  print_buffer_len(spi_rx_process_buffer, data_len, ">>   App::processSpiRxMsg()");
#endif  // DEBUG_DFU_BUFFER

  bool debug = false;
  // bool debug = true;
  if (debug) {
    if (valid_spi_data) {
      printSpiBuffers();
    }
  }

// disable DFU if see values from K66 main application
#ifndef USE_MOCK_TARGET
  if (dfu_manager_.isActive()) {
    received_ack_packet_ = checkFor2MatchingBytesSpiRxBuffer(0x5a, 0xa1);
    // @TODO(ks) - get proto val
    if (dfu_manager_.isDfuProcessCompleted()) {
      if (spi_rx_process_buffer[0] == 0x80) {
        NRF_LOG_INFO(">>   App::processSpiRxMsg(): Received 0x80 from Target: Exit DFU Mode ");
        dfu_manager_.setActive(false);
      }
    }
  }
#endif  // USE_MOCK_TARGET

  // empty ble tx ring buffer first (keep message in FIFO order)
  if (dfu_manager_.isActive()) {
#ifdef USE_NEW_SPI_PROTOCOL
    dfu_manager_.handleSpiRx(&spi_rx_process_buffer[3], data_len);
#else
    dfu_manager_.handleSpiRx(spi_rx_process_buffer, data_len);
#endif  // USE_NEW_SPI_PROTOCOL
  } else {
    if (valid_spi_data) {
      const uint8_t yr_header_id = spi_rx_process_buffer[0];
      switch (yr_header_id) {
        case 60: {  // @TODO(ks): hardcoded MB -> NRF CMD ID
          NRF_LOG_INFO(">>   App::processSpiRxMsg(): Received ID: ([%d] [%02X]) len: [%d]", yr_header_id, yr_header_id,
                       data_len);
          // ProcessSerialNumberCmd(&spi_rx_process_buffer[3], data_len);
          WritePairedDeviceSerialNumberToFlash((char *)(&spi_rx_process_buffer[3]), data_len);
        } break;
        default: {
          // memset(spi_tx_buffer, 0, sizeof(spi_tx_buffer));  // @TODO(ks)
          // memcpy(ble_tx_buffer, spi_rx_process_buffer, sizeof(spi_rx_process_buffer));
          bool sent = sendBluetoothMsgBuffer(spi_rx_process_buffer, total_len);
          // bool sent = sendBluetoothMsgDirect(spi_rx_process_buffer, total_len);
        } break;
      }
    }
  }

#ifdef DEBUG_DFU_BUFFER
  NRF_LOG_INFO(">>   App::processSpiRxMsg(): clearSpiRxProcessBuffer()");
#endif  // DEBUG_DFU_BUFFER
  clearSpiRxProcessBuffer();
}

void AppMainBoard::ProcessSerialNumberCmd(uint8_t *buf, const size_t len) {
  char str_buf[kMaxSerialNumberLength];
  memset(str_buf, '\0', sizeof(str_buf));
  memcpy(str_buf, buf, len - 1);
  bool valid_serial_number = DoesBufferContainDigit(str_buf, sizeof(str_buf));
  NRF_LOG_INFO(">>   App::processSerialNumberCmd(): Input: Len: [%d] str: [%s] valid: [%d]", len, str_buf,
               valid_serial_number);
  NRF_LOG_FLUSH();
  if (valid_serial_number) {
    ReadSerialNumberFromFlash();
    bool serial_number_changed = strcmp(serial_number_str_, str_buf) != 0;
    NRF_LOG_INFO(">>   App::processSerialNumberCmd(): Input: [%s], In-flash: [%s], Changed: [%d]", str_buf,
                 serial_number_str_, serial_number_changed);
    NRF_LOG_FLUSH();
    if (serial_number_changed) {
      WriteSerialNumberToFlash(str_buf, sizeof(str_buf));
      // UpdateBleAdvertisingName();
    }
  }
}

#include "yr_util.h"

void AppMainBoard::UpdateBleAdvertisingName() {
  uint32_t err_code;
  ble_gap_conn_params_t gap_conn_params;
  ble_gap_conn_sec_mode_t sec_mode;

  // sd_ble_gap_adv_stop(m_advertising.adv_handle);

  BLE_GAP_CONN_SEC_MODE_SET_OPEN(&sec_mode);

  char new_device_name[50];
  memset(new_device_name, '\0', sizeof(new_device_name));
  // snprintf(new_device_name, sizeof(new_device_name), "%s %s", DEVICE_NAME, serial_number_str_);
  snprintf(new_device_name, sizeof(new_device_name), "%s %s", DEVICE_NAME, "test");

  NRF_LOG_INFO("Setting Device Name (update): [%s]", new_device_name);
  NRF_LOG_FLUSH();

  err_code = sd_ble_gap_device_name_set(&sec_mode, (const uint8_t *)new_device_name, strlen(new_device_name));
  APP_ERROR_CHECK(err_code);

  // memset(&gap_conn_params, 0, sizeof(gap_conn_params));

  // gap_conn_params.min_conn_interval = MIN_CONN_INTERVAL;
  // gap_conn_params.max_conn_interval = MAX_CONN_INTERVAL;
  // gap_conn_params.slave_latency = SLAVE_LATENCY;
  // gap_conn_params.conn_sup_timeout = CONN_SUP_TIMEOUT;

  // err_code = sd_ble_gap_ppcp_set(&gap_conn_params);
  // APP_ERROR_CHECK(err_code);

  // Encode the advertising packet and start advertising
  // ble_advdata_encode(&advdata, m_advertising.adv_data.adv_data.p_data, &m_advertising.adv_data.adv_data.len);
  // sd_ble_gap_adv_set_configure(&m_adv_handle, &m_advertising.adv_data, &adv_params);
  // advertising_data_set();
  NRF_LOG_INFO("advertising_stop()");
  NRF_LOG_FLUSH();
  // sd_ble_gap_adv_stop(m_advertising.adv_handle);
  // sd_ble_gap_adv_stop(m_adv_handle);
  // advertising_data_set();

  // advertising_init();
  // sd_ble_gap_adv_start(m_advertising.adv_handle, APP_BLE_CONN_CFG_TAG);
  // sd_ble_gap_adv_start(m_adv_handle, APP_BLE_CONN_CFG_TAG);

  // err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_ADV, m_advertising.adv_handle, 4);

  NRF_LOG_INFO("advertising_start() 2");
  NRF_LOG_FLUSH();
  // sd_ble_gap_adv_start(m_advertising.adv_handle, APP_BLE_CONN_CFG_TAG);
  // advertising_start(false);

  /* Store Current connection state od adv lib */
  // uint16_t m_adv_current_slave_link_conn_handle_saved = m_advertising.current_slave_link_conn_handle;

  /* Re-initialize advertising module */
  // advertising_init();

  /* Re-store Current connection state lib */
  // m_advertising.current_slave_link_conn_handle = m_adv_current_slave_link_conn_handle_saved;

  // UNUSED_RETURN_VALUE(sd_ble_gap_adv_stop(m_adv_handle));
  // err_code = sd_ble_gap_adv_start(m_adv_handle, APP_BLE_CONN_CFG_TAG);
  // APP_ERROR_CHECK(err_code);
}

void AppMainBoard::handleTxComplete(uint32_t count) {
  msg_stats[BLE_TX_COMPLETED].add(count);
}


//----------------------------------------------------------------------------//
//  UPDATE LOOP
//----------------------------------------------------------------------------//
void AppMainBoard::update() {
  uint32_t now_ms = millis();
  uint32_t dt_update = now_ms - time_last_update_;
  const uint32_t delay_ms = 100;
  const uint32_t delay_spi_ms = 10;
  // const uint32_t delay_ms = 500;
  // const uint32_t delay_spi_ms = 20;

#ifndef USE_MOCK_TARGET
  // BLE RX PROCESS
  // if (is_ble_connected_.getVal()) {
  // copy BLE Rx Buffer -> SPI Tx Buffer
  bool need_process_ble_rx = emptyBleRxBuffer();
  if (need_process_ble_rx) {
    processBleRxMsg();
  }
  // if (dfu_manager_.isActive()) {
  //   // if (msg_stats[BLE_TX].getDt() > 10) {
  //   dfu_manager_.handleBleRx();
  //   // }
  // }
  // }
#endif  // USE_MOCK_TARGET

  // SPI RX
  bool processed_spi_rx = false;
  bool need_process_spi_rx = emptySpiRxBuffer();
  if (need_process_spi_rx) {
    processSpiRxMsg();
    processed_spi_rx = true;
  }

  if (dt_update > delay_ms) {
    time_last_update_ = millis();


#ifdef USE_MOCK_TARGET
    // if (STATE_RECEIVED == spi_tx_state_.getVal()) {
    mock_target_.onReceivePacket(&spi_tx_buffer[1], spi_tx_buffer[0]);
    // spi_tx_state_.setVal(STATE_PROCESSED);
    clearSpiTxBuffer();
    // }
#endif  // USE_MOCK_TARGET

    // DFU UPDATE
    if (dfu_manager_.isActive()) {
      if (!processed_spi_rx) {
#ifdef USE_MOCK_TARGET
        NRF_LOG_INFO("     App::update(): DfuManager::update()");
#endif  // USE_MOCK_TARGET
        dfu_manager_.update();
        if (dfu_manager_.inFlashingState()) {
          dfu_manager_.checkIfNeedRequestNewPacketFromClient();
        }
      }
    }

#ifdef USE_MOCK_TARGET
    mock_target_.update();
#endif  // USE_MOCK_TARGET
#ifdef USE_MOCK_TARGET_AUTO_START
#ifdef USE_MOCK_TARGET
    if (!mock_target_.isRunning()) {
      dfu_manager_.resetK66AndEnterDfuStateMachine();
      dfu_manager_.setActive(true);
      mock_target_.reset();
    }
#else
    if (loop_count_ == 5) {
      dfu_manager_.resetK66AndEnterDfuStateMachine();
      dfu_manager_.setActive(true);
    }
#endif  // USE_MOCK_TARGET
#endif  // USE_MOCK_TARGET_AUTO_START

    // if (STATE_RECEIVED == spi_tx_state_.getVal()) {
    //   spi_tx_state_.setVal(STATE_PROCESSED);
    //   setSpiBuffers();
    //   toggleSpiInterrupt();
    //   // clearSpiTxBuffer();
    // }

    runSlowLoop();

    loop_count_++;
  }

  // BLE TX EMPTY
  if (!ble_tx_packet_ringbuf.isEmpty()) {
    // if (loop_count_ % 10 == 0) {
    emptyBleTxRingBuffer();
    // }
  }

  // if (!dfu_manager_.isActive() || (dfu_manager_.isActive() && (dt_spi_rx > delay_spi_ms))) {
  bool send_spi_tx = true;
  uint32_t dt_spi_rx = millis() - time_last_received_spi_msg_;
  uint32_t dt_spi_tx = millis() - time_last_sent_spi_msg_;
  if (dfu_manager_.isActive()) {
    send_spi_tx = ((dt_spi_rx > delay_spi_ms) && (dt_spi_tx > delay_spi_ms));
  } else {
    send_spi_tx = false;
  }
#ifdef USE_FASTER_DFU
  if (received_ack_packet_) {
    send_spi_tx = true;
    received_ack_packet_ = false;
  }
#endif  // USE_FASTER_DFU
  if (send_spi_tx) {
    bool need_process_spi_tx = emptySpiTxBuffer();
    if (need_process_spi_tx) {
#ifdef DEBUG_DFU_SPI_TIMER
      if (spi_debug_timers_[spi_debug_timer_t::SPI_DBG_RX_1] != 0) {
        NRF_LOG_INFO("\r\n\t\t\tSPI TIMERS [%d] [%d %d %d] ms\r\n", spi_debug_timers_[0], spi_debug_timers_[1],
                     spi_debug_timers_[2], spi_debug_timers_[3])
        resetSpiDebugTimers();
      }
      spi_debug_timers_[spi_debug_timer_t::SPI_DBG_TX] = millis();
#endif  // DEBUG_DFU_SPI_TIMER
      time_last_sent_spi_msg_ = millis();
      if (using_tx_interrupt_) {
        toggleSpiInterrupt();
#ifdef DEBUG_DFU_SPI_INTERRUPT
        NRF_LOG_INFO("<<   App::toggleSpiInterrupt dt=[%d ms] sent=[%d ms]", dt_spi_rx, dt_spi_tx);
#endif  // DEBUG_DFU_SPI_INTERRUPT
      }
    }
  }

#ifdef USE_READ_REQUEST
  if (!dfu_manager_.isActive()) {
    // request by pulling pin active if have >0 messages
    bool needs_read_request = !spi_tx_packet_ringbuf.isEmpty();
    read_request_status_.setVal(needs_read_request ? READ_REQUEST_SET : READ_REQUEST_UNSET);

    enableBleIntPin(needs_read_request ? READ_REQUEST_ACTIVE_STATE : !READ_REQUEST_ACTIVE_STATE);

    // check if changed
    if (read_request_status_.changed()) {
#ifdef DEBUG_READ_REQUEST
      NRF_LOG_INFO("              Read Request Status changed [%d] -> [%d]\r\n", read_request_status_.getValLast(),
                   read_request_status_.getVal());
#endif  // DEBUG_READ_REQUEST
    }
    read_request_status_.update();
  }
#endif  // USE_READ_REQUEST

  if (is_ble_connected_.changed()) {
    NRF_LOG_INFO("\tBLE connected changed [%d] -> [%d]", is_ble_connected_.getValLast() ? 1 : 0,
                 is_ble_connected_.getVal() ? 1 : 0);
    if (is_ble_connected_.getVal()) {
      // setSpiBuffers();
      // nrf_drv_spis_uninit(&spis);
      // APP_ERROR_CHECK(nrf_drv_spis_init(&spis, &spis_config, spis_event_handler));
      // NRF_LOG_INFO("\tRe-initialized SPI\r\n");
      // setSpiBuffers();
    }
  }
  is_ble_connected_.update();

  if (is_ble_tx_ready_.changed()) {
    NRF_LOG_INFO("\tBLE TX Ready changed [%d] -> [%d]", is_ble_tx_ready_.getValLast() ? 1 : 0,
                 is_ble_tx_ready_.getVal() ? 1 : 0);
  }

  is_ble_tx_ready_.update();

  NRF_LOG_FLUSH();
}

void AppMainBoard::runSlowLoop() {
  // if (!dfu_manager_.isActive()) {
  uint32_t dt_print = millis() - time_last_print_;
  if (dt_print > 1000) {
#ifndef USE_DFU_TEST_COMM
#ifdef PRINT_COMM_RATES
    printCommStatus();
#endif  // PRINT_COMM_RATES
#endif  // USE_DFU_TEST_COMM
    time_last_print_ = millis();
  }
  // }
}

//----------------------------------------------------------------------------//
//  BUFFER UTILS
//----------------------------------------------------------------------------//
void AppMainBoard::clearSpiTxBuffer() {
  memset(spi_tx_buffer, 0, sizeof(spi_tx_buffer));
}

void AppMainBoard::clearSpiRxBuffer() {
  memset(spi_rx_buffer, 0, sizeof(spi_rx_buffer));
}

void AppMainBoard::clearSpiRxProcessBuffer() {
  memset(spi_rx_process_buffer, 0, sizeof(spi_rx_process_buffer));
}

void AppMainBoard::clearSpiTxProcessBuffer() {
  memset(spi_tx_process_buffer, 0, sizeof(spi_tx_process_buffer));
}

void AppMainBoard::clearBleRxBuffer() {
  memset(ble_rx_buffer, 0, sizeof(ble_rx_buffer));
}

void AppMainBoard::clearBleRxProcessBuffer() {
  memset(ble_rx_process_buffer, 0, sizeof(ble_rx_process_buffer));
}

//----------------------------------------------------------------------------//
//  UTIL
//----------------------------------------------------------------------------//
bool AppMainBoard::checkFor2MatchingBytesSpiRxBuffer(const uint8_t &b0_in, const uint8_t &b1_in) {
  return checkFor2MatchingBytesBuffer(b0_in, b1_in, spi_rx_process_buffer, sizeof(spi_rx_process_buffer));
}

void AppMainBoard::printCommStatus() {
  // NRF_LOG_INFO("BLE Rx [%lu] Tx [%lu] SPI Rx [%lu] Tx [%lu]", msg_stats[BLE_RX].getCount(),
  // msg_stats[BLE_TX].getCount(),
  //        msg_stats[SPI_RX].getCount(), msg_stats[SPI_TX].getCount());
  for (uint32_t i = 0; i < NUM_MSG_TYPES; i++) {
    msg_stats[i].update();
  }
  // NRF_LOG_INFO("BLE Rx [%.2f] Tx [%.2f] SPI Rx [%.2f] Tx [%.2f]", msg_stats[BLE_RX].getRate(),
  //        msg_stats[BLE_TX].getRate(), msg_stats[SPI_RX].getRate(), msg_stats[SPI_TX].getRate());

  uint32_t ms = millis();
  uint32_t seconds = (ms / 1000) % 60;
  uint32_t minutes = (ms / (1000 * 60)) % 60;
  uint32_t hours = (ms / (1000 * 3600)) % 24;
  uint32_t lost_in_air_packets =
      static_cast<int>(msg_stats[BLE_TX].getCount()) - static_cast<int>(msg_stats[BLE_TX_COMPLETED].getCount());

  static char buf_time[20];
  memset(buf_time, '\0', sizeof(buf_time));
  snprintf(buf_time, sizeof(buf_time), "%02lu:%02lu:%02lu", hours, minutes, seconds);

  static char buf_rates[100];
  memset(buf_rates, '\0', sizeof(buf_rates));
  snprintf(buf_rates, sizeof(buf_rates), "BLE Rx [%02d] Tx [%02d | %02d | %02d]\tSPI Rx [%02d] Tx [%02d]",
           static_cast<int>(msg_stats[BLE_RX].getRate()), static_cast<int>(msg_stats[BLE_TX].getRate()),
           static_cast<int>(msg_stats[BLE_TX_ATTEMPT].getRate()),
           static_cast<int>(msg_stats[BLE_TX_BUF_OFLOWED].getCount()), static_cast<int>(msg_stats[SPI_RX].getRate()),
           static_cast<int>(msg_stats[SPI_TX].getRate()));

#ifdef PRINT_COMM_RATES_MSG_1
  NRF_LOG_INFO("[%s] %s", buf_time, buf_rates);
  // NRF_LOG_FLUSH();
#endif  // PRINT_COMM_RATES_MSG_1

#ifdef PRINT_COMM_RATES_MSG_2
  NRF_LOG_INFO("BLE TX: [txed %d |Acked %d] TxRingBuf [clred %d |oflowed %d] SPI [%d]",
               static_cast<int>(msg_stats[BLE_TX].getRate()), static_cast<int>(msg_stats[BLE_TX_COMPLETED].getRate()),
               static_cast<int>(msg_stats[BLE_TX_BUF_CLEARED].getRate()),
               static_cast<int>(msg_stats[BLE_TX_BUF_OFLOWED].getCount()),
               static_cast<int>(msg_stats[SPI_RX].getRate()))
  NRF_LOG_INFO("BLE TX: Total Packets:%d, UnAck'ed Packets:%d PacketsLostSPI: %d",
               static_cast<int>(msg_stats[BLE_TX].getCount()), lost_in_air_packets,
               msg_stats[SPI_MISSING_SEQ_NOS].getCount())
  NRF_LOG_INFO("BLE RX: %d, SPI TX: %d", static_cast<int>(msg_stats[BLE_RX].getRate()), msg_stats[SPI_TX].getRate())
  NRF_LOG_FLUSH();
#endif  // PRINT_COMM_RATES_MSG_2
}

void AppMainBoard::emptyBleTxRingBuffer() {
  size_t num = ble_tx_packet_ringbuf.numElements();
  for (size_t i = 0; i < num; i++) {
    if (ble_tx_packet_ringbuf.isEmpty()) {
      msg_stats[BLE_TX_BUF_CLEARED].add();
      break;
    }
    ble_packet_t *packet_ptr = ble_tx_packet_ringbuf.peek(0);
#ifdef DEBUG_DFU_BUFFER
    NRF_LOG_INFO("txbuf - empty packet [%d]", ble_tx_packet_ringbuf.numElements());
#endif  // DEBUG_DFU_BUFFER
    bool sent_buf = sendBluetoothMsgDirect(packet_ptr->data, packet_ptr->length);
    if (sent_buf) {
      // msg_stats[BLE_TX_FROM_BUF].add();
      ble_packet_t tmp;
      ble_tx_packet_ringbuf.pull(&tmp);
#ifdef DEBUG_DFU_BUFFER
      NRF_LOG_INFO("txbuf - empty packet success [%d]", ble_tx_packet_ringbuf.numElements());
#endif  // DEBUG_DFU_BUFFER
    } else {
      break;
    }
#ifdef TX_BUFFER_EMPTY_ONLY_ONE
    break;
#endif  // TX_BUFFER_EMPTY_ONLY_ONE
  }
  if (ble_tx_packet_ringbuf.isEmpty()) {
    msg_stats[BLE_TX_BUF_CLEARED].add();
  }
}

// #define NRF_SUCCESS                           (NRF_ERROR_BASE_NUM + 0)  ///< Successful command
// #define NRF_ERROR_SVC_HANDLER_MISSING         (NRF_ERROR_BASE_NUM + 1)  ///< SVC handler is missing
// #define NRF_ERROR_SOFTDEVICE_NOT_ENABLED      (NRF_ERROR_BASE_NUM + 2)  ///< SoftDevice has not been enabled
// #define NRF_ERROR_INTERNAL                    (NRF_ERROR_BASE_NUM + 3)  ///< Internal Error
// #define NRF_ERROR_NO_MEM                      (NRF_ERROR_BASE_NUM + 4)  ///< No Memory for operation
// #define NRF_ERROR_NOT_FOUND                   (NRF_ERROR_BASE_NUM + 5)  ///< Not found
// #define NRF_ERROR_NOT_SUPPORTED               (NRF_ERROR_BASE_NUM + 6)  ///< Not supported
// #define NRF_ERROR_INVALID_PARAM               (NRF_ERROR_BASE_NUM + 7)  ///< Invalid Parameter
// #define NRF_ERROR_INVALID_STATE               (NRF_ERROR_BASE_NUM + 8)  ///< Invalid state, operation disallowed in
// this state #define NRF_ERROR_INVALID_LENGTH              (NRF_ERROR_BASE_NUM + 9)  ///< Invalid Length #define
// NRF_ERROR_INVALID_FLAGS               (NRF_ERROR_BASE_NUM + 10) ///< Invalid Flags #define NRF_ERROR_INVALID_DATA
// (NRF_ERROR_BASE_NUM + 11) ///< Invalid Data #define NRF_ERROR_DATA_SIZE                   (NRF_ERROR_BASE_NUM + 12)
// ///< Invalid Data size #define NRF_ERROR_TIMEOUT                     (NRF_ERROR_BASE_NUM + 13) ///< Operation timed
// out #define NRF_ERROR_NULL                        (NRF_ERROR_BASE_NUM + 14) ///< Null Pointer #define
// NRF_ERROR_FORBIDDEN                   (NRF_ERROR_BASE_NUM + 15) ///< Forbidden Operation #define
// NRF_ERROR_INVALID_ADDR                (NRF_ERROR_BASE_NUM + 16) ///< Bad Memory Address #define NRF_ERROR_BUSY
// (NRF_ERROR_BASE_NUM + 17) ///< Busy #define NRF_ERROR_CONN_COUNT                  (NRF_ERROR_BASE_NUM + 18) ///<
// Maximum connection count exceeded. #define NRF_ERROR_RESOURCES                   (NRF_ERROR_BASE_NUM + 19) ///< Not
// enough resources for operation

// void AppMainBoard::processSpi() {
//   uint32_t now_ms = millis();
//   uint32_t dt_toggle_int = now_ms - time_last_toggle_interrupt_;
//   if (using_tx_interrupt_) {
//     if (spi_state.getVal() == SPI_SET) {
//       if (dt_toggle_int > 0) {
//         // spi_state.setVal(SPI_SENT);
//         toggleSpiInterrupt();
//       }
//     }
//     if (toggle_spi_interrupt_count_ % 2 == 1) {
//       // NRF_LOG_INFO("toggleSpiInt [%lu] [%lu]", toggle_spi_interrupt_count_, dt_toggle_int);
//       toggle_spi_interrupt_count_++;
//     }
//   }
//
//   // memcpy(spi_tx_buffer_last, spi_tx_buffer, sizeof(spi_tx_buffer));
//   // memcpy(spi_rx_buffer_last, spi_rx_buffer, sizeof(spi_rx_buffer));
//
//   // memset(spi_rx_buffer, 0, sizeof(spi_rx_buffer));
//
//   if (spi_state.changed()) {
//     // NRF_LOG_INFO("\tSPI State change [%s] -> [%s]", spi_state_str(spi_state.getValLast()),
//     // spi_state_str(spi_state.getVal()));
//   }
//   spi_state.update();
// }

//----------------------------------------------------------------------------//
//  éĺŻščŽžĺ¤ĺşĺĺˇçŽĄçĺ˝ďż
//----------------------------------------------------------------------------//

void AppMainBoard::ReadPairedDeviceSerialNumberFromFlash() {
  uint32_t err_code;
  fds_record_desc_t desc = { 0 };
  fds_find_token_t tok = { 0 };
  err_code = fds_record_find(CONFIG_FILE, CONFIG_REC_KEY, &desc, &tok);
  NRF_LOG_INFO("Reading Paired Device Serial Number from Flash.");

  if (err_code == NRF_SUCCESS) {
    fds_flash_record_t config = { 0 };
    err_code = fds_record_open(&desc, &config);
    APP_ERROR_CHECK(err_code);
    memcpy(&yr_fds_config_struct, config.p_data, sizeof(yr_fds_config_struct_t));

    // ĺ¤ĺśéĺŻščŽžĺ¤ĺşĺĺˇĺ°ćŹĺ°ĺé
    memcpy(paired_device_serial_number_str_, yr_fds_config_struct.paired_device_serial_number,
           sizeof(paired_device_serial_number_str_));

    // ćŁćĽĺşĺĺˇćŻĺŚćć
    if (IsPairedDeviceSerialNumberValid()) {
      NRF_LOG_INFO("Config file found, paired device serial_number: [%s].",
                   yr_fds_config_struct.paired_device_serial_number);
    } else {
      NRF_LOG_INFO("Config file found, but paired device serial_number is invalid (all 0xFF).");
    }

    err_code = fds_record_close(&desc);
    APP_ERROR_CHECK(err_code);
  } else {
    NRF_LOG_INFO("Config file not found, paired device serial_number not available.");
    // čŽžç˝Žä¸şć ďż
    memset(paired_device_serial_number_str_, 0xFF, sizeof(paired_device_serial_number_str_));
  }
}

void AppMainBoard::WritePairedDeviceSerialNumberToFlash(char *paired_serial_number_str, const size_t len) {
  uint32_t err_code;
  fds_record_desc_t desc = { 0 };
  fds_find_token_t tok = { 0 };
  err_code = fds_record_find(CONFIG_FILE, CONFIG_REC_KEY, &desc, &tok);
  NRF_LOG_INFO("Writing Paired Device Serial Number to Flash: [%s]", paired_serial_number_str);

  // ć¸çŠşĺšśĺ¤ĺść°çéĺŻščŽžĺ¤ĺşĺĺˇ
  memset(yr_fds_config_struct.paired_device_serial_number, '\0',
         sizeof(yr_fds_config_struct.paired_device_serial_number));
  strncpy(yr_fds_config_struct.paired_device_serial_number, paired_serial_number_str,
          len < sizeof(yr_fds_config_struct.paired_device_serial_number) ? len : sizeof(yr_fds_config_struct.paired_device_serial_number) - 1);

  // // ĺćść´ć°ćŹĺ°ĺé
  // memset(paired_device_serial_number_str_, '\0', sizeof(paired_device_serial_number_str_));
  // strncpy(paired_device_serial_number_str_, paired_serial_number_str,
  //         len < sizeof(paired_device_serial_number_str_) ? len : sizeof(paired_device_serial_number_str_) - 1);

  if (err_code == NRF_SUCCESS) {
    // ć´ć°Flashä¸­çčŽ°ĺ˝
    err_code = fds_record_update(&desc, &m_dummy_fds_record);
    if ((err_code != NRF_SUCCESS) && (err_code == FDS_ERR_NO_SPACE_IN_FLASH)) {
      NRF_LOG_INFO("No space in flash, delete some records to update the config file.");
    } else {
      APP_ERROR_CHECK(err_code);
      NRF_LOG_INFO("Paired device serial number written to flash successfully.");
    }
  } else {
    NRF_LOG_ERROR("Config record not found, cannot write paired device serial number.");
  }
}

bool AppMainBoard::IsPairedDeviceSerialNumberValid() {
  // ćŁćĽĺşĺĺˇćŻĺŚĺ¨ä¸ş0xFFďźć ćçśćďź
  for (size_t i = 0; i < sizeof(paired_device_serial_number_str_); i++) {
    if ((uint8_t)paired_device_serial_number_str_[i] != 0xFF) {
      return true;  // ćžĺ°ďż˝xFFĺ­çŹŚďźĺşĺĺˇćć
    }
  }
  return false;  // ĺ¨ä¸ş0xFFďźĺşĺĺˇć ć
}

const char* AppMainBoard::GetPairedDeviceSerialNumber() {
  return paired_device_serial_number_str_;
}
