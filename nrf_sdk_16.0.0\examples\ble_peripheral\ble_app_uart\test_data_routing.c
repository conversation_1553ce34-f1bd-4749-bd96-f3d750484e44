/**
 * @file test_data_routing.c
 * @brief Test functions for BLE Central+Peripheral data routing functionality
 */

#include "ble_central_peripheral.h"
#include "nrf_log.h"
#include "app_timer.h"

// Test configuration
#define TEST_INTERVAL_MS 10000  // Run tests every 10 seconds
#define TEST_DATA_MAX_SIZE 100

// Test state
static uint32_t test_cycle_counter = 0;
static bool test_mode_enabled = false;

/**
 * @brief Simple test data generator
 */
static void generate_test_data(uint8_t *buffer, uint16_t max_size, const char *prefix) {
    // Simple sprintf alternative to avoid dependencies
    uint8_t prefix_len = 0;
    while (prefix[prefix_len] != '\0' && prefix_len < max_size - 20) {
        buffer[prefix_len] = prefix[prefix_len];
        prefix_len++;
    }
    
    // Add cycle counter
    buffer[prefix_len++] = ' ';
    buffer[prefix_len++] = '#';
    
    // Simple number to string conversion
    uint32_t num = test_cycle_counter;
    uint8_t digits[10];
    uint8_t digit_count = 0;
    
    if (num == 0) {
        digits[0] = '0';
        digit_count = 1;
    } else {
        while (num > 0) {
            digits[digit_count++] = '0' + (num % 10);
            num /= 10;
        }
    }
    
    // Reverse digits
    for (int i = digit_count - 1; i >= 0; i--) {
        buffer[prefix_len++] = digits[i];
    }
    
    buffer[prefix_len] = '\0';
}

/**
 * @brief Test basic connectivity
 */
void test_basic_connectivity(void) {
    NRF_LOG_INFO("=== Basic Connectivity Test ===");
    
    bool phone_connected = isPeripheralConnected();
    bool device_connected = isCentralConnected();
    bool central_mode = getCentralMode();
    
    NRF_LOG_INFO("Phone connected: %s", phone_connected ? "YES" : "NO");
    NRF_LOG_INFO("Device connected: %s", device_connected ? "YES" : "NO");
    NRF_LOG_INFO("Central mode: %s", central_mode ? "ENABLED" : "DISABLED");
    
    if (!phone_connected && !device_connected) {
        NRF_LOG_WARNING("No connections available for testing");
        return;
    }
    
    // Test basic data sending
    uint8_t test_data[TEST_DATA_MAX_SIZE];
    
    if (phone_connected) {
        generate_test_data(test_data, sizeof(test_data), "Test to phone");
        uint32_t result = sendDataToPhone(0x03, test_data, 20); // DATA_TYPE_LOCAL_PROCESS
        NRF_LOG_INFO("Phone test result: 0x%08X", result);
    }
    
    if (device_connected) {
        generate_test_data(test_data, sizeof(test_data), "Test to device");
        uint32_t result = sendDataToDevice(0x03, test_data, 21); // DATA_TYPE_LOCAL_PROCESS
        NRF_LOG_INFO("Device test result: 0x%08X", result);
    }
}

/**
 * @brief Test data forwarding functionality
 */
void test_data_forwarding(void) {
    NRF_LOG_INFO("=== Data Forwarding Test ===");
    
    if (!isPeripheralConnected() || !isCentralConnected()) {
        NRF_LOG_WARNING("Both connections required for forwarding test");
        return;
    }
    
    uint8_t forward_data[TEST_DATA_MAX_SIZE];
    
    // Test phone -> device forwarding
    generate_test_data(forward_data, sizeof(forward_data), "Forward P->D");
    uint32_t result = sendDataToDevice(0x02, forward_data, 25); // DATA_TYPE_FORWARD_TO_DEVICE
    NRF_LOG_INFO("Phone->Device forward result: 0x%08X", result);
    
    // Test device -> phone forwarding
    generate_test_data(forward_data, sizeof(forward_data), "Forward D->P");
    result = sendDataToPhone(0x01, forward_data, 25); // DATA_TYPE_FORWARD_TO_PHONE
    NRF_LOG_INFO("Device->Phone forward result: 0x%08X", result);
}

/**
 * @brief Test heartbeat and status functionality
 */
void test_heartbeat_status(void) {
    NRF_LOG_INFO("=== Heartbeat & Status Test ===");
    
    // Send heartbeat
    sendHeartbeat();
    NRF_LOG_INFO("Heartbeat sent");
    
    // Send status
    sendStatusInfo();
    NRF_LOG_INFO("Status info sent");
}

/**
 * @brief Test protocol data types
 */
void test_protocol_data_types(void) {
    NRF_LOG_INFO("=== Protocol Data Types Test ===");
    
    uint8_t test_data[50];
    
    if (isPeripheralConnected()) {
        // Test different data types to phone
        generate_test_data(test_data, sizeof(test_data), "Local");
        sendDataToPhone(0x03, test_data, 15); // LOCAL_PROCESS
        
        generate_test_data(test_data, sizeof(test_data), "Status");
        sendDataToPhone(0x05, test_data, 16); // STATUS
    }
    
    if (isCentralConnected()) {
        // Test different data types to device
        generate_test_data(test_data, sizeof(test_data), "Local");
        sendDataToDevice(0x03, test_data, 15); // LOCAL_PROCESS
        
        generate_test_data(test_data, sizeof(test_data), "Status");
        sendDataToDevice(0x05, test_data, 16); // STATUS
    }
}

/**
 * @brief Run comprehensive test suite
 */
void run_comprehensive_test(void) {
    test_cycle_counter++;
    
    NRF_LOG_INFO("========================================");
    NRF_LOG_INFO("Starting Test Cycle #%d", test_cycle_counter);
    NRF_LOG_INFO("========================================");
    
    // Run all tests
    test_basic_connectivity();
    test_data_forwarding();
    test_heartbeat_status();
    test_protocol_data_types();
    
    NRF_LOG_INFO("========================================");
    NRF_LOG_INFO("Test Cycle #%d Complete", test_cycle_counter);
    NRF_LOG_INFO("========================================");
}

/**
 * @brief Enable/disable test mode
 */
void set_test_mode(bool enable) {
    test_mode_enabled = enable;
    NRF_LOG_INFO("Test mode %s", enable ? "ENABLED" : "DISABLED");
    
    if (enable) {
        test_cycle_counter = 0;
        NRF_LOG_INFO("Test mode will run every %d seconds", TEST_INTERVAL_MS / 1000);
    }
}

/**
 * @brief Check if it's time to run tests (call this periodically)
 */
void check_test_schedule(void) {
    static uint32_t last_test_time = 0;
    
    if (!test_mode_enabled) {
        return;
    }
    
    uint32_t current_time = app_timer_cnt_get();
    uint32_t elapsed_ms = app_timer_cnt_diff_compute(current_time, last_test_time) / 32.768;
    
    if (elapsed_ms >= TEST_INTERVAL_MS) {
        run_comprehensive_test();
        last_test_time = current_time;
    }
}

/**
 * @brief Manual test trigger (for debugging)
 */
void trigger_manual_test(void) {
    NRF_LOG_INFO("Manual test triggered");
    run_comprehensive_test();
}

/**
 * @brief Test connection management
 */
void test_connection_management(void) {
    NRF_LOG_INFO("=== Connection Management Test ===");
    
    // Log current state
    NRF_LOG_INFO("Current connections:");
    NRF_LOG_INFO("  Phone: %s", isPeripheralConnected() ? "Connected" : "Disconnected");
    NRF_LOG_INFO("  Device: %s", isCentralConnected() ? "Connected" : "Disconnected");
    NRF_LOG_INFO("  Central mode: %s", getCentralMode() ? "Enabled" : "Disabled");
    
    // Test mode switching (if needed)
    if (!getCentralMode()) {
        NRF_LOG_INFO("Enabling central mode for testing");
        setCentralMode(1);
    }
}

/**
 * @brief Initialize test system
 */
void init_test_system(void) {
    NRF_LOG_INFO("Initializing data routing test system");
    test_cycle_counter = 0;
    test_mode_enabled = false;
    
    // Run initial connection test
    test_connection_management();
    
    NRF_LOG_INFO("Test system ready. Use set_test_mode(true) to enable automatic testing");
}
