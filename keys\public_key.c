
/* This file was automatically generated by nrfutil on 2019-10-31 (YY-MM-DD) at 18:37:56 */

#include "stdint.h"
#include "compiler_abstraction.h"

/** @brief Public key used to verify DFU images */
__ALIGN(4) const uint8_t pk[64] =
{
    0x92, 0xca, 0xb5, 0x4c, 0x9d, 0x06, 0xa2, 0x52, 0x5b, 0xe8, 0x45, 0xa8, 0x2d, 0xc5, 0xf9, 0xe2, 0xe0, 0x88, 0x11, 0x0e, 0x9a, 0xb0, 0x9e, 0x96, 0xa0, 0xf9, 0x48, 0x94, 0x22, 0x8c, 0x6a, 0x6f, 
    0xc0, 0xe9, 0xe2, 0x43, 0x63, 0x48, 0x79, 0xfb, 0x9e, 0x74, 0x94, 0xaf, 0x20, 0xcc, 0x4c, 0xb5, 0xb2, 0xfc, 0x55, 0x46, 0xd4, 0x24, 0x40, 0x46, 0xdc, 0x69, 0x29, 0x74, 0x6f, 0x8e, 0x44, 0x0d
};
