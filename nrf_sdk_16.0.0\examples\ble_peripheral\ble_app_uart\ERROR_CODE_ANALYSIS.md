# BLE连接错误代码深度分析

## 错误代码准确定义

根据您提供的nRF错误代码定义：

### 当前遇到的错误
- **0x12** = `NRF_ERROR_BASE_NUM + 18` = `NRF_ERROR_CONN_COUNT` 
  - **含义**：连接数量超过最大限制
  - **原因**：BLE堆栈认为已达到最大连接数

- **0x10** = `NRF_ERROR_BASE_NUM + 16` = `NRF_ERROR_INVALID_ADDR`
  - **含义**：无效的内存地址或BLE地址
  - **原因**：地址类型不正确或地址格式有问题

## 从日志发现的问题

### 1. 设备名称截断
```
Manual name extraction: YRob (type: 0x09, len: 24)
```
- **问题**：24字节的名称被截断为"YRob"
- **原因**：缓冲区大小限制
- **影响**：可能影响设备识别

### 2. 连接数量限制错误
```
sd_ble_gap_connect() with custom params failed: 0x12
```
- **问题**：`NRF_ERROR_CONN_COUNT`
- **原因**：可能已有隐藏的连接或资源未释放

### 3. 地址类型错误
```
sd_ble_gap_connect() with defaults also failed: 0x10 (OTHER)
```
- **问题**：`NRF_ERROR_INVALID_ADDR`
- **原因**：地址类型可能不正确

## 已实施的修复

### 1. 设备名称截断修复
```cpp
// 改进的名称提取，处理长名称
if (name_len > 0 && name_len < (sizeof(dev_name) - 1)) {
  memcpy(dev_name, &p_adv->data.p_data[pos + 2], name_len);
  dev_name[name_len] = '\0';
} else {
  // 处理名称过长的情况
  uint8_t max_len = sizeof(dev_name) - 1;
  memcpy(dev_name, &p_adv->data.p_data[pos + 2], max_len);
  dev_name[max_len] = '\0';
}
```

### 2. 连接数量管理
```cpp
// 主动断开现有central连接
if (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) {
  NRF_LOG_WARNING("Already connected as central, disconnecting first");
  sd_ble_gap_disconnect(m_central_conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
  nrf_delay_ms(100);
  m_central_conn_handle = BLE_CONN_HANDLE_INVALID;
}
```

### 3. 地址类型修复
```cpp
// 创建地址副本并修正地址类型
ble_gap_addr_t peer_addr = p_adv->peer_addr;

// 验证并修正地址类型
if (peer_addr.addr_type > BLE_GAP_ADDR_TYPE_RANDOM_PRIVATE_NON_RESOLVABLE) {
  NRF_LOG_WARNING("Invalid address type %d, setting to PUBLIC", peer_addr.addr_type);
  peer_addr.addr_type = BLE_GAP_ADDR_TYPE_PUBLIC;
}
```

### 4. 增强的调试信息
```cpp
NRF_LOG_INFO("Address type: %d", p_adv->peer_addr.addr_type);
NRF_LOG_INFO("Original addr type: %d", peer_addr.addr_type);
```

## 预期的新日志输出

重新测试后应该看到：

```
Manual name extraction: YRobot_FE:8E:36:B5:36:D9 (type: 0x09, len: 24)
Found device: YRobot_FE:8E:36:B5:36:D9 (name_found: 1)
MAC: FE:8E:36:B5:36:D9
RSSI: -XX dBm, Data len: 29
Address type: X
Adv type: 0x01, Connectable: YES
Original addr type: X
Stopped scanning (result: 0x0), attempting connection...
Connection params: min=6, max=24, latency=0, timeout=400
Attempting connection with custom parameters...
```

然后可能的结果：
- **成功**：`Connection request sent successfully` + `BLE Connected as Central`
- **改进的错误信息**：更准确的错误分析

## 可能的结果分析

### 结果A：连接成功
如果修复有效，应该看到成功的连接。

### 结果B：仍有NRF_ERROR_CONN_COUNT
**可能原因**：
1. SoftDevice配置的连接数限制
2. 内存分配不足
3. 事件处理队列满

**进一步解决方案**：
```c
// 检查sdk_config.h中的配置
#define NRF_SDH_BLE_CENTRAL_LINK_COUNT 1
#define NRF_SDH_BLE_PERIPHERAL_LINK_COUNT 1
#define NRF_SDH_BLE_TOTAL_LINK_COUNT 2
```

### 结果C：仍有NRF_ERROR_INVALID_ADDR
**可能原因**：
1. 地址类型仍然不正确
2. 地址解析问题
3. 硬件地址问题

**进一步解决方案**：
尝试强制使用特定地址类型：
```cpp
peer_addr.addr_type = BLE_GAP_ADDR_TYPE_RANDOM_STATIC;
```

## 测试建议

### 1. 立即测试
重新编译并观察：
- 完整的设备名称是否正确显示
- 地址类型信息
- 连接尝试的详细过程

### 2. 对比测试
使用手机nRF Connect：
1. 扫描同一设备
2. 查看设备的地址类型
3. 尝试连接并对比结果

### 3. 配置检查
验证sdk_config.h中的BLE配置：
```c
#define NRF_SDH_BLE_CENTRAL_LINK_COUNT 1
#define NRF_SDH_BLE_PERIPHERAL_LINK_COUNT 1
#define NRF_SDH_BLE_TOTAL_LINK_COUNT 2
#define NRF_SDH_BLE_GAP_EVENT_LENGTH 6
```

## 成功连接的标志

当所有修复生效时，应该看到：

```
Manual name extraction: YRobot_FE:8E:36:B5:36:D9 (type: 0x09, len: 24)
Found device: YRobot_FE:8E:36:B5:36:D9 (name_found: 1)
MAC: FE:8E:36:B5:36:D9
RSSI: -XX dBm, Data len: 29
Address type: 1
Adv type: 0x01, Connectable: YES
Original addr type: 1
Stopped scanning (result: 0x0), attempting connection...
Connection params: min=6, max=24, latency=0, timeout=400
Attempting connection with custom parameters...
Connection request sent successfully
BLE Connected as Central
```

## 如果问题仍然存在

### 最后的解决方案
1. **重启目标设备**：清除其连接状态
2. **检查硬件**：确认天线和RF路径
3. **使用标准测试设备**：用另一个nRF52作为peripheral测试
4. **检查SoftDevice版本**：确认兼容性

这次的修复应该能解决连接数量限制和地址类型问题，大大提高连接成功率。
