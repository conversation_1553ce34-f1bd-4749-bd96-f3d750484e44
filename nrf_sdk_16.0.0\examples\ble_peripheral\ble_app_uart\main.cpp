#include "yr_util.h"
#include "app_main_board.h"

nrf_drv_spis_config_t spis_config;

//----------------------------------------------------------------------------//
//  BLE RX
//----------------------------------------------------------------------------//
static void nus_data_handler(ble_nus_evt_t *p_evt) {
  if (p_evt->type == BLE_NUS_EVT_RX_DATA) {
    app_main_board.handleBleRx((uint8_t *)p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
    bool debug = false;
    if (debug) {
      NRF_LOG_INFO("Received data from BLE NUS. Writing data on UART.");
      NRF_LOG_HEXDUMP_INFO(p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
    }
  }
}

//----------------------------------------------------------------------------//
//  SPI RX
//----------------------------------------------------------------------------//
void spis_event_handler(nrf_drv_spis_event_t event) {
#ifdef USE_MOCK_TARGET
  return;
#endif  // USE_MOCK_TARGET
  if (event.evt_type == NRF_DRV_SPIS_XFER_DONE) {
    app_main_board.handleSpiRx(event.rx_amount);
  } else if (event.evt_type == NRF_DRV_SPIS_BUFFERS_SET_DONE) {
    // NRF_LOG_INFO("-- SPI BUFFERS SET [%d]", app_main_board.msg_stats[AppMainBoard::SPI_TX].getCount());
    app_main_board.msg_stats[AppMainBoard::SPI_TX].add();
  }
}

uint32_t sendBluetoothMsgProxy(uint8_t *buf, uint16_t len) {
  return g_send_bluetooth(buf, len);
}

void idle_state_handle(void) {
  app_main_board.update();
}

void timer_main_loop_handler(void *p_context) {
}

void handleConnection(bool state) {
  if (state) {
    g_time_connected = millis();
    app_main_board.handleConnected();
  } else {
    app_main_board.handleDisconnected();
  }
}

#ifdef __cplusplus
extern "C" {
#endif

static void ble_evt_handler(ble_evt_t const *p_ble_evt, void *p_context) {
  uint32_t err_code;
  uint32_t count = 0;

  ble_gap_evt_t const *p_gap_evt = &p_ble_evt->evt.gap_evt;

  // NRF_LOG_INFO("\t\tble_evt_handler [%d]", p_ble_evt->header.evt_id);

  switch (p_ble_evt->header.evt_id) {
    case BLE_GATTS_EVT_HVN_TX_COMPLETE:
      count = p_ble_evt->evt.gatts_evt.params.hvn_tx_complete.count;
      app_main_board.handleTxComplete(count);
      // NRF_LOG_INFO("BLE TX Complete. N:%d",count)
      break;

    case BLE_GAP_EVT_CONNECTED:
      NRF_LOG_INFO("BLE Connected");
      err_code = bsp_indication_set(BSP_INDICATE_CONNECTED);
      APP_ERROR_CHECK(err_code);
      m_conn_handle = p_ble_evt->evt.gap_evt.conn_handle;
      err_code = nrf_ble_qwr_conn_handle_assign(&m_qwr, m_conn_handle);
      APP_ERROR_CHECK(err_code);
      handleConnection(true);

      // err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_CONN, m_conn_handle, 4);
      // APP_ERROR_CHECK(err_code);

      err_code = sd_ble_gap_phy_update(p_gap_evt->conn_handle, &m_test_params.phys);
      APP_ERROR_CHECK(err_code);
      //
      // err_code = nrf_ble_gatt_data_length_set(&m_gatt, BLE_CONN_HANDLE_INVALID, 251);
      // APP_ERROR_CHECK(err_code);
      break;

    case BLE_GAP_EVT_DISCONNECTED:
      NRF_LOG_INFO("BLE Disonnected");
      NRF_LOG_INFO("BLE Disconnect reason %d", p_ble_evt->evt.gap_evt.params.disconnected.reason);
      // LED indication will be changed when advertising starts.
      m_conn_handle = BLE_CONN_HANDLE_INVALID;
      handleConnection(false);
      break;

    case BLE_GAP_EVT_PHY_UPDATE_REQUEST: {
      NRF_LOG_INFO("PHY update request.");
      // ble_gap_phys_t phys = {
      //   //        .rx_phys = BLE_GAP_PHY_AUTO,
      //   //        .tx_phys = BLE_GAP_PHY_AUTO,
      // };
      // phys.rx_phys = BLE_GAP_PHY_AUTO;
      // phys.tx_phys = BLE_GAP_PHY_AUTO;
      // err_code = sd_ble_gap_phy_update(p_ble_evt->evt.gap_evt.conn_handle, &phys);
      ble_gap_evt_t const *p_gap_evt = &p_ble_evt->evt.gap_evt;
      err_code = sd_ble_gap_phy_update(p_gap_evt->conn_handle, &m_test_params.phys);
      APP_ERROR_CHECK(err_code);
    } break;
    case BLE_GAP_EVT_SEC_PARAMS_REQUEST:
      NRF_LOG_INFO("BLE_GAP_EVT_SEC_PARAMS_REQUEST");
      // Pairing not supported
      err_code = sd_ble_gap_sec_params_reply(m_conn_handle, BLE_GAP_SEC_STATUS_PAIRING_NOT_SUPP, NULL, NULL);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GATTS_EVT_SYS_ATTR_MISSING:
      NRF_LOG_INFO("GATTS_EVT_SYS_ATTR_MISSING");
      // No system attributes have been stored.
      err_code = sd_ble_gatts_sys_attr_set(m_conn_handle, NULL, 0, 0);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GATTC_EVT_TIMEOUT:
      // Disconnect on GATT Client timeout event.
      NRF_LOG_INFO("GATT Client Timeout.");
      err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gattc_evt.conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GATTS_EVT_TIMEOUT:
      // Disconnect on GATT Server timeout event.
      NRF_LOG_INFO("GATT Server Timeout.");
      err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gatts_evt.conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GAP_EVT_AUTH_KEY_REQUEST:
      NRF_LOG_INFO("BLE_GAP_EVT_AUTH_KEY_REQUEST");
      break;
    case BLE_GAP_EVT_LESC_DHKEY_REQUEST:
      NRF_LOG_INFO("BLE_GAP_EVT_LESC_DHKEY_REQUEST");
      break;
    case BLE_GAP_EVT_AUTH_STATUS:
      NRF_LOG_INFO("BLE_GAP_EVT_AUTH_STATUS: status=0x%x bond=0x%x lv4: %d kdist_own:0x%x kdist_peer:0x%x",
                   p_ble_evt->evt.gap_evt.params.auth_status.auth_status,
                   p_ble_evt->evt.gap_evt.params.auth_status.bonded,
                   p_ble_evt->evt.gap_evt.params.auth_status.sm1_levels.lv4,
                   *((uint8_t *)&p_ble_evt->evt.gap_evt.params.auth_status.kdist_own),
                   *((uint8_t *)&p_ble_evt->evt.gap_evt.params.auth_status.kdist_peer));
      break;
    default:
      // No implementation needed.
      break;
  }
}


/**@brief Function for the SoftDevice initialization.
 *
 * @details This function initializes the SoftDevice and the BLE event interrupt.
 */
void ble_stack_init(void) {
  ret_code_t err_code;

  // NRF_LOG_INFO("ble_stack_init");

  err_code = nrf_sdh_enable_request();
  APP_ERROR_CHECK(err_code);

  // Configure the BLE stack using the default settings.
  // Fetch the start address of the application RAM.
  uint32_t ram_start = 0;
  err_code = nrf_sdh_ble_default_cfg_set(APP_BLE_CONN_CFG_TAG, &ram_start);
  APP_ERROR_CHECK(err_code);

  ble_cfg_t ble_cfg;
  memset(&ble_cfg, 0, sizeof(ble_cfg));
  ble_cfg.conn_cfg.conn_cfg_tag = APP_BLE_CONN_CFG_TAG;
  // ble_cfg.conn_cfg.params.gattc_conn_cfg.write_cmd_tx_queue_size = 10;
  // ble_cfg.conn_cfg.params.gattc_conn_cfg.write_cmd_tx_queue_size = 8;
  // ble_cfg.conn_cfg.params.gatts_conn_cfg.hvn_tx_queue_size = 10;
  // ble_cfg.conn_cfg.params.gatts_conn_cfg.hvn_tx_queue_size = 7;
  err_code = sd_ble_cfg_set(BLE_CONN_CFG_GATTC, &ble_cfg, ram_start);
  APP_ERROR_CHECK(err_code);

  // Enable BLE stack.
  err_code = nrf_sdh_ble_enable(&ram_start);
  APP_ERROR_CHECK(err_code);

  // ble_opt_t gap_opt;
  // gap_opt.gap_opt.ext_len.rxtx_max_pdu_payload_size = 251;  // Example: set max length to 54bytes
  // err_code = sd_ble_opt_set(BLE_GAP_OPT_EXT_LEN, &gap_opt);
  // APP_ERROR_CHECK(err_code);

  // Register a handler for BLE events.
  NRF_SDH_BLE_OBSERVER(m_ble_observer, APP_BLE_OBSERVER_PRIO, ble_evt_handler, NULL);
}

#ifdef __cplusplus
}
#endif

bool initSpi(void) {
  spis_config.csn_pin = SPIS_CSN_PIN;
  spis_config.sck_pin = SPIS_SCK_PIN;
  spis_config.mosi_pin = SPIS_MOSI_PIN;
  spis_config.miso_pin = SPIS_MISO_PIN;
  spis_config.mode = NRF_SPIS_MODE_0;
  spis_config.bit_order = NRF_SPIS_BIT_ORDER_MSB_FIRST;
  spis_config.csn_pullup = NRFX_SPIS_DEFAULT_CSN_PULLUP;
  spis_config.miso_drive = NRFX_SPIS_DEFAULT_MISO_DRIVE;
  spis_config.def = NRFX_SPIS_DEFAULT_DEF;
  spis_config.orc = NRFX_SPIS_DEFAULT_ORC;
  spis_config.irq_priority = NRFX_SPIS_DEFAULT_CONFIG_IRQ_PRIORITY;

  APP_ERROR_CHECK(nrf_drv_spis_init(&spis, &spis_config, spis_event_handler));
  NRF_LOG_INFO("Init SPIS");
  return true;
}

bool initBle(void) {
  bool is_central = false;
  bool erase_bonds = false;
  ret_code_t err_code;

  ble_stack_init();
  gap_params_init();
  gatt_init();
  services_init();
  advertising_init();
  conn_params_init();

  gatt_mtu_set(m_test_params.att_mtu);
  conn_evt_len_ext_set(m_test_params.conn_evt_len_ext_enabled);

  print_ble_config();

  // sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_CONN, BLE_CONN_HANDLE_INVALID, transmitPower);
  err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_ADV, m_advertising.adv_handle, 4);
  APP_ERROR_CHECK(err_code);
  // err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_ADV, BLE_CONN_HANDLE_INVALID, 1);

  // Start execution.
  NRF_LOG_INFO("\r\nUART started.");
  NRF_LOG_INFO("Debug logging for UART over RTT started.");
  // advertising_start(false);
  // erase_bonds = true;
  if (is_central) {
    scan_init();
    scan_start();
  } 
  advertising_start(erase_bonds);
  return true;
}

/**@brief Application main function.
 */
int main(void) {
  bool erase_bonds;
  ret_code_t err_code;

  log_init();
  //  NRF_LOG_DEFAULT_BACKENDS_INIT();

#ifdef USE_NRF_DFU
  // Initialize the async SVCI interface to bootloader before any interrupts are enabled.
  err_code = ble_dfu_buttonless_async_svci_init();
  APP_ERROR_CHECK(err_code);
#endif  // USE_NRF_DFU

  timers_init();
  buttons_leds_init(&erase_bonds);

#ifdef USE_UART
  uart_init();
#endif  // USE_UART

  power_management_init();

#ifdef USE_NRF_DFU
  peer_manager_init();
#endif  // USE_NRF_DFU
        // peer_manager_init();

#ifdef USE_FDS
  app_main_board.InitFds();
#endif  // USE_FDS

  initBle();
  initSpi();

  nrf_gpio_cfg_output(PIN_SPI_WRITE_INT);
  // app_main_board.enableBleIntPin(true);
  nrf_gpio_cfg_output(PIN_RESET_K66);
  nrf_gpio_pin_write(PIN_RESET_K66, 1);
  app_main_board.init();

  // Start application timers.
  err_code = app_timer_start(sd_timer_id, YR_MICROS_INTERVAL, NULL);
  APP_ERROR_CHECK(err_code);
  err_code = app_timer_start(main_loop_timer_id, YR_MICROS_INTERVAL, NULL);
  APP_ERROR_CHECK(err_code);

  // Enter main loop.
  for (;;) {
    idle_state_handle();
  }
}


/**
 * @}
 */
