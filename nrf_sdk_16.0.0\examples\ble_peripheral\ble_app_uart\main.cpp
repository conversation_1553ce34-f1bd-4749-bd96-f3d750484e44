#include "yr_util.h"
#include "app_main_board.h"
#include "ble_central_peripheral.h"

nrf_drv_spis_config_t spis_config;

//----------------------------------------------------------------------------//
//  CENTRAL MODE VARIABLES
//----------------------------------------------------------------------------//
NRF_BLE_SCAN_DEF(m_scan);                                  /**< Scanning module instance. */
static bool is_central = false;                            /**< Flag to enable/disable central mode. */
static char m_target_periph_name[] = "YRobot";            /**< Name of the device to search for. */
static uint16_t m_central_conn_handle = BLE_CONN_HANDLE_INVALID; /**< Handle for central connection. */

//----------------------------------------------------------------------------//
//  DEVICE DISCOVERY TRACKING
//----------------------------------------------------------------------------//
#define MAX_DISCOVERED_DEVICES 20
typedef struct {
  ble_gap_addr_t addr;
  char name[64];
  int8_t rssi;
  bool valid;
} discovered_device_t;

static discovered_device_t discovered_devices[MAX_DISCOVERED_DEVICES];
static uint8_t discovered_count = 0;

//----------------------------------------------------------------------------//
//  BLE RX
//----------------------------------------------------------------------------//
void nus_data_handler(ble_nus_evt_t *p_evt) {
  if (p_evt->type == BLE_NUS_EVT_RX_DATA) {
    app_main_board.handleBleRx((uint8_t *)p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
    bool debug = false;
    if (debug) {
      NRF_LOG_INFO("Received data from BLE NUS. Writing data on UART.");
      NRF_LOG_HEXDUMP_INFO(p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
    }
  }
}

//----------------------------------------------------------------------------//
//  SPI RX
//----------------------------------------------------------------------------//
void spis_event_handler(nrf_drv_spis_event_t event) {
#ifdef USE_MOCK_TARGET
  return;
#endif  // USE_MOCK_TARGET
  if (event.evt_type == NRF_DRV_SPIS_XFER_DONE) {
    app_main_board.handleSpiRx(event.rx_amount);
  } else if (event.evt_type == NRF_DRV_SPIS_BUFFERS_SET_DONE) {
    // NRF_LOG_INFO("-- SPI BUFFERS SET [%d]", app_main_board.msg_stats[AppMainBoard::SPI_TX].getCount());
    app_main_board.msg_stats[AppMainBoard::SPI_TX].add();
  }
}

uint32_t sendBluetoothMsgProxy(uint8_t *buf, uint16_t len) {
  return g_send_bluetooth(buf, len);
}

/**@brief Function to send data via central connection.
 */
unsigned int sendBluetoothMsgCentral(unsigned char *buf, unsigned short len) {
  if (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) {
    uint16_t length = len;
    return ble_nus_data_send(&m_nus, buf, &length, m_central_conn_handle);
  }
  return NRF_ERROR_INVALID_STATE;
}

/**@brief Function to check if central connection is active.
 */
int isCentralConnected(void) {
  return (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) ? 1 : 0;
}

/**@brief Function to check if peripheral connection is active.
 */
int isPeripheralConnected(void) {
  return (m_conn_handle != BLE_CONN_HANDLE_INVALID) ? 1 : 0;
}

void idle_state_handle(void) {
  app_main_board.update();
}

void timer_main_loop_handler(void *p_context) {
}

//----------------------------------------------------------------------------//
//  CENTRAL MODE FUNCTIONS
//----------------------------------------------------------------------------//
/**@brief Function to check if device is already discovered.
 */
static bool is_device_already_discovered(const ble_gap_addr_t* addr) {
  for (uint8_t i = 0; i < discovered_count; i++) {
    if (discovered_devices[i].valid &&
        memcmp(&discovered_devices[i].addr, addr, sizeof(ble_gap_addr_t)) == 0) {
      return true;
    }
  }
  return false;
}

/**@brief Function to add device to discovered list.
 */
static void add_discovered_device(const ble_gap_addr_t* addr, const char* name, int8_t rssi) {
  if (discovered_count < MAX_DISCOVERED_DEVICES) {
    discovered_devices[discovered_count].addr = *addr;
    strncpy(discovered_devices[discovered_count].name, name, sizeof(discovered_devices[discovered_count].name) - 1);
    discovered_devices[discovered_count].name[sizeof(discovered_devices[discovered_count].name) - 1] = '\0';
    discovered_devices[discovered_count].rssi = rssi;
    discovered_devices[discovered_count].valid = true;
    discovered_count++;
  }
}

/**@brief Function to clear discovered devices list.
 */
static void clear_discovered_devices(void) {
  memset(discovered_devices, 0, sizeof(discovered_devices));
  discovered_count = 0;
  NRF_LOG_INFO("Discovered devices list cleared");
}

/**@brief Function to print all discovered devices.
 */
static void print_discovered_devices(void) {
  NRF_LOG_INFO("=== Discovered YRobot Devices Summary ===");
  NRF_LOG_INFO("Total devices found: %d", discovered_count);

  for (uint8_t i = 0; i < discovered_count; i++) {
    if (discovered_devices[i].valid) {
      NRF_LOG_INFO("[%d] %s - %02X:%02X:%02X:%02X:%02X:%02X (RSSI: %d dBm)",
                   i + 1,
                   discovered_devices[i].name,
                   discovered_devices[i].addr.addr[5], discovered_devices[i].addr.addr[4],
                   discovered_devices[i].addr.addr[3], discovered_devices[i].addr.addr[2],
                   discovered_devices[i].addr.addr[1], discovered_devices[i].addr.addr[0],
                   discovered_devices[i].rssi);
    }
  }
  NRF_LOG_INFO("=======================================");
}

/**@brief Function to check if a string contains a substring (case insensitive).
 */
static bool string_contains(const char* haystack, const char* needle) {
  if (!haystack || !needle) return false;

  size_t haystack_len = strlen(haystack);
  size_t needle_len = strlen(needle);

  if (needle_len > haystack_len) return false;

  for (size_t i = 0; i <= haystack_len - needle_len; i++) {
    bool match = true;
    for (size_t j = 0; j < needle_len; j++) {
      char h = haystack[i + j];
      char n = needle[j];
      // Convert to lowercase for case-insensitive comparison
      if (h >= 'A' && h <= 'Z') h += 32;
      if (n >= 'A' && n <= 'Z') n += 32;
      if (h != n) {
        match = false;
        break;
      }
    }
    if (match) return true;
  }
  return false;
}

/**@brief Function for handling Scanning Module events.
 */
static void scan_evt_handler(scan_evt_t const * p_scan_evt) {
  switch(p_scan_evt->scan_evt_id) {
    case NRF_BLE_SCAN_EVT_NOT_FOUND: {
      // Handle all advertising reports, not just filtered ones
      ble_gap_evt_adv_report_t const * p_adv = p_scan_evt->params.p_not_found;

      if (p_adv && p_adv->data.len > 0) {
        char dev_name[64] = {0};  // Increased buffer size

        // Try to extract device name from advertising data
        bool name_found = ble_advdata_name_find(p_adv->data.p_data, p_adv->data.len, dev_name);

        if (name_found && strlen(dev_name) > 0) {
          // Check if device name contains "YRobot" (case insensitive)
          if (string_contains(dev_name, "YRobot")) {
            // Check if this device is already discovered (avoid duplicates)
            if (!is_device_already_discovered(&p_adv->peer_addr)) {
              // Add to discovered devices list
              add_discovered_device(&p_adv->peer_addr, dev_name, p_adv->rssi);

              // Print device information
              NRF_LOG_INFO("=== NEW YRobot Device Found ===");
              NRF_LOG_INFO("Name: %s", dev_name);
              NRF_LOG_INFO("MAC:  %02X:%02X:%02X:%02X:%02X:%02X",
                           p_adv->peer_addr.addr[5], p_adv->peer_addr.addr[4], p_adv->peer_addr.addr[3],
                           p_adv->peer_addr.addr[2], p_adv->peer_addr.addr[1], p_adv->peer_addr.addr[0]);
              NRF_LOG_INFO("RSSI: %d dBm", p_adv->rssi);
              NRF_LOG_INFO("Total found: %d", discovered_count);
              NRF_LOG_INFO("==============================");
            }
          }
        }
      }
    } break;

    case NRF_BLE_SCAN_EVT_FILTER_MATCH: {
      // This case is now handled by NRF_BLE_SCAN_EVT_NOT_FOUND
      // We don't want to connect, so we don't handle this case
    } break;

    case NRF_BLE_SCAN_EVT_NOT_FOUND: {
      NRF_LOG_DEBUG("No matching device found during scan.");
    } break;

    default:
      break;
  }
}

/**@brief Function for initialization the scanning (no filters for discovery mode).
 */
static void scan_init(void) {
  ret_code_t err_code;

  // Initialize scanning module without filters to receive all advertising reports
  err_code = nrf_ble_scan_init(&m_scan, NULL, scan_evt_handler);
  APP_ERROR_CHECK(err_code);

  // Don't set any filters - we want to see all devices
  // The filtering will be done in the event handler based on device name content

  NRF_LOG_INFO("Scan initialized for discovery mode (no filters)");
}

/**@brief Function for starting the scanning.
 */
static void scan_start(void) {
  NRF_LOG_INFO("Starting scanning for devices containing 'YRobot' in name...");

  ret_code_t err_code = nrf_ble_scan_start(&m_scan);
  APP_ERROR_CHECK(err_code);
}

void handleConnection(bool state) {
  if (state) {
    g_time_connected = millis();
    app_main_board.handleConnected();
  } else {
    app_main_board.handleDisconnected();
  }
}

#ifdef __cplusplus
extern "C" {
#endif

static void ble_evt_handler(ble_evt_t const *p_ble_evt, void *p_context) {
  uint32_t err_code;
  uint32_t count = 0;

  ble_gap_evt_t const *p_gap_evt = &p_ble_evt->evt.gap_evt;

  // NRF_LOG_INFO("\t\tble_evt_handler [%d]", p_ble_evt->header.evt_id);

  switch (p_ble_evt->header.evt_id) {
    case BLE_GATTS_EVT_HVN_TX_COMPLETE:
      count = p_ble_evt->evt.gatts_evt.params.hvn_tx_complete.count;
      app_main_board.handleTxComplete(count);
      // NRF_LOG_INFO("BLE TX Complete. N:%d",count)
      break;

    case BLE_GAP_EVT_CONNECTED: {
      uint16_t conn_handle = p_ble_evt->evt.gap_evt.conn_handle;
      uint8_t role = p_ble_evt->evt.gap_evt.params.connected.role;

      if (role == BLE_GAP_ROLE_PERIPH) {
        NRF_LOG_INFO("BLE Connected as Peripheral");
        m_conn_handle = conn_handle;
        err_code = nrf_ble_qwr_conn_handle_assign(&m_qwr, m_conn_handle);
        APP_ERROR_CHECK(err_code);
      } else if (role == BLE_GAP_ROLE_CENTRAL) {
        NRF_LOG_INFO("BLE Connected as Central");
        m_central_conn_handle = conn_handle;
        // Stop scanning when connected as central
        nrf_ble_scan_stop();
      }

      err_code = bsp_indication_set(BSP_INDICATE_CONNECTED);
      APP_ERROR_CHECK(err_code);
      handleConnection(true);

      // err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_CONN, conn_handle, 4);
      // APP_ERROR_CHECK(err_code);

      err_code = sd_ble_gap_phy_update(p_gap_evt->conn_handle, &m_test_params.phys);
      APP_ERROR_CHECK(err_code);
      //
      // err_code = nrf_ble_gatt_data_length_set(&m_gatt, BLE_CONN_HANDLE_INVALID, 251);
      // APP_ERROR_CHECK(err_code);
    } break;

    case BLE_GAP_EVT_DISCONNECTED: {
      uint16_t conn_handle = p_ble_evt->evt.gap_evt.conn_handle;

      NRF_LOG_INFO("BLE Disconnected");
      NRF_LOG_INFO("BLE Disconnect reason %d", p_ble_evt->evt.gap_evt.params.disconnected.reason);

      // Check which connection was disconnected
      if (conn_handle == m_conn_handle) {
        NRF_LOG_INFO("Peripheral connection disconnected");
        m_conn_handle = BLE_CONN_HANDLE_INVALID;
      } else if (conn_handle == m_central_conn_handle) {
        NRF_LOG_INFO("Central connection disconnected");
        m_central_conn_handle = BLE_CONN_HANDLE_INVALID;

        // Restart scanning if central mode is enabled
        if (is_central) {
          scan_start();
        }
      }

      // LED indication will be changed when advertising starts.
      handleConnection(false);
    } break;

    case BLE_GAP_EVT_PHY_UPDATE_REQUEST: {
      NRF_LOG_INFO("PHY update request.");
      // ble_gap_phys_t phys = {
      //   //        .rx_phys = BLE_GAP_PHY_AUTO,
      //   //        .tx_phys = BLE_GAP_PHY_AUTO,
      // };
      // phys.rx_phys = BLE_GAP_PHY_AUTO;
      // phys.tx_phys = BLE_GAP_PHY_AUTO;
      // err_code = sd_ble_gap_phy_update(p_ble_evt->evt.gap_evt.conn_handle, &phys);
      ble_gap_evt_t const *p_gap_evt = &p_ble_evt->evt.gap_evt;
      err_code = sd_ble_gap_phy_update(p_gap_evt->conn_handle, &m_test_params.phys);
      APP_ERROR_CHECK(err_code);
    } break;
    case BLE_GAP_EVT_SEC_PARAMS_REQUEST:
      NRF_LOG_INFO("BLE_GAP_EVT_SEC_PARAMS_REQUEST");
      // Pairing not supported
      err_code = sd_ble_gap_sec_params_reply(m_conn_handle, BLE_GAP_SEC_STATUS_PAIRING_NOT_SUPP, NULL, NULL);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GATTS_EVT_SYS_ATTR_MISSING:
      NRF_LOG_INFO("GATTS_EVT_SYS_ATTR_MISSING");
      // No system attributes have been stored.
      err_code = sd_ble_gatts_sys_attr_set(m_conn_handle, NULL, 0, 0);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GATTC_EVT_TIMEOUT:
      // Disconnect on GATT Client timeout event.
      NRF_LOG_INFO("GATT Client Timeout.");
      err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gattc_evt.conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
      APP_ERROR_CHECK(err_code);
      break;

    case BLE_GAP_EVT_ADV_REPORT:
      // This event is handled by the scanning module
      break;
    case BLE_GATTS_EVT_TIMEOUT:
      // Disconnect on GATT Server timeout event.
      NRF_LOG_INFO("GATT Server Timeout.");
      err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gatts_evt.conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GAP_EVT_AUTH_KEY_REQUEST:
      NRF_LOG_INFO("BLE_GAP_EVT_AUTH_KEY_REQUEST");
      break;
    case BLE_GAP_EVT_LESC_DHKEY_REQUEST:
      NRF_LOG_INFO("BLE_GAP_EVT_LESC_DHKEY_REQUEST");
      break;
    case BLE_GAP_EVT_AUTH_STATUS:
      NRF_LOG_INFO("BLE_GAP_EVT_AUTH_STATUS: status=0x%x bond=0x%x lv4: %d kdist_own:0x%x kdist_peer:0x%x",
                   p_ble_evt->evt.gap_evt.params.auth_status.auth_status,
                   p_ble_evt->evt.gap_evt.params.auth_status.bonded,
                   p_ble_evt->evt.gap_evt.params.auth_status.sm1_levels.lv4,
                   *((uint8_t *)&p_ble_evt->evt.gap_evt.params.auth_status.kdist_own),
                   *((uint8_t *)&p_ble_evt->evt.gap_evt.params.auth_status.kdist_peer));
      break;
    default:
      // No implementation needed.
      break;
  }
}


/**@brief Function for the SoftDevice initialization.
 *
 * @details This function initializes the SoftDevice and the BLE event interrupt.
 */
void ble_stack_init(void) {
  ret_code_t err_code;

  // NRF_LOG_INFO("ble_stack_init");

  err_code = nrf_sdh_enable_request();
  APP_ERROR_CHECK(err_code);

  // Configure the BLE stack using the default settings.
  // Fetch the start address of the application RAM.
  uint32_t ram_start = 0;
  err_code = nrf_sdh_ble_default_cfg_set(APP_BLE_CONN_CFG_TAG, &ram_start);
  APP_ERROR_CHECK(err_code);

  ble_cfg_t ble_cfg;
  memset(&ble_cfg, 0, sizeof(ble_cfg));
  ble_cfg.conn_cfg.conn_cfg_tag = APP_BLE_CONN_CFG_TAG;
  // ble_cfg.conn_cfg.params.gattc_conn_cfg.write_cmd_tx_queue_size = 10;
  // ble_cfg.conn_cfg.params.gattc_conn_cfg.write_cmd_tx_queue_size = 8;
  // ble_cfg.conn_cfg.params.gatts_conn_cfg.hvn_tx_queue_size = 10;
  // ble_cfg.conn_cfg.params.gatts_conn_cfg.hvn_tx_queue_size = 7;
  err_code = sd_ble_cfg_set(BLE_CONN_CFG_GATTC, &ble_cfg, ram_start);
  APP_ERROR_CHECK(err_code);

  // Enable BLE stack.
  err_code = nrf_sdh_ble_enable(&ram_start);
  APP_ERROR_CHECK(err_code);

  // ble_opt_t gap_opt;
  // gap_opt.gap_opt.ext_len.rxtx_max_pdu_payload_size = 251;  // Example: set max length to 54bytes
  // err_code = sd_ble_opt_set(BLE_GAP_OPT_EXT_LEN, &gap_opt);
  // APP_ERROR_CHECK(err_code);

  // Register a handler for BLE events.
  NRF_SDH_BLE_OBSERVER(m_ble_observer, APP_BLE_OBSERVER_PRIO, ble_evt_handler, NULL);
}

#ifdef __cplusplus
}
#endif

bool initSpi(void) {
  spis_config.csn_pin = SPIS_CSN_PIN;
  spis_config.sck_pin = SPIS_SCK_PIN;
  spis_config.mosi_pin = SPIS_MOSI_PIN;
  spis_config.miso_pin = SPIS_MISO_PIN;
  spis_config.mode = NRF_SPIS_MODE_0;
  spis_config.bit_order = NRF_SPIS_BIT_ORDER_MSB_FIRST;
  spis_config.csn_pullup = NRFX_SPIS_DEFAULT_CSN_PULLUP;
  spis_config.miso_drive = NRFX_SPIS_DEFAULT_MISO_DRIVE;
  spis_config.def = NRFX_SPIS_DEFAULT_DEF;
  spis_config.orc = NRFX_SPIS_DEFAULT_ORC;
  spis_config.irq_priority = NRFX_SPIS_DEFAULT_CONFIG_IRQ_PRIORITY;

  APP_ERROR_CHECK(nrf_drv_spis_init(&spis, &spis_config, spis_event_handler));
  NRF_LOG_INFO("Init SPIS");
  return true;
}

bool initBle(void) {
  bool erase_bonds = false;
  ret_code_t err_code;

  ble_stack_init();
  gap_params_init();
  gatt_init();
  services_init();
  advertising_init();
  conn_params_init();

  gatt_mtu_set(m_test_params.att_mtu);
  conn_evt_len_ext_set(m_test_params.conn_evt_len_ext_enabled);

  print_ble_config();

  // sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_CONN, BLE_CONN_HANDLE_INVALID, transmitPower);
  err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_ADV, m_advertising.adv_handle, 4);
  APP_ERROR_CHECK(err_code);
  // err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_ADV, BLE_CONN_HANDLE_INVALID, 1);

  // Start execution.
  NRF_LOG_INFO("\r\nUART started.");
  NRF_LOG_INFO("Debug logging for UART over RTT started.");

  // Initialize and start central mode if enabled
  if (is_central) {
    NRF_LOG_INFO("Initializing Central mode");
    scan_init();
    scan_start();
  } else {
    NRF_LOG_INFO("Central mode disabled");
  }

  // Always start advertising (peripheral mode)
  NRF_LOG_INFO("Starting Peripheral mode (advertising)");
  advertising_start(erase_bonds);

  return true;
}

/**@brief Function to enable/disable central mode.
 */
void setCentralMode(int enable) {
  is_central = (enable != 0);
  NRF_LOG_INFO("Central mode %s", is_central ? "ENABLED" : "DISABLED");
}

/**@brief Function to get current central mode status.
 */
int getCentralMode(void) {
  return is_central ? 1 : 0;
}

/**@brief Function to start YRobot device discovery.
 */
void startYRobotDiscovery(void) {
  if (getCentralMode()) {
    clear_discovered_devices();
    scan_start();
    NRF_LOG_INFO("YRobot discovery started");
  } else {
    NRF_LOG_WARNING("Central mode not enabled. Call setCentralMode(1) first.");
  }
}

/**@brief Function to stop YRobot device discovery.
 */
void stopYRobotDiscovery(void) {
  nrf_ble_scan_stop();
  NRF_LOG_INFO("YRobot discovery stopped");
  print_discovered_devices();
}

/**@brief Function to get discovered devices count.
 */
int getDiscoveredDevicesCount(void) {
  return discovered_count;
}

/**@brief Test function to demonstrate central/peripheral functionality.
 */
void testCentralPeripheralMode(void) {
  static uint32_t test_counter = 0;
  test_counter++;

  NRF_LOG_INFO("=== Central/Peripheral Test #%d ===", test_counter);
  NRF_LOG_INFO("Central mode: %s", getCentralMode() ? "ENABLED" : "DISABLED");
  NRF_LOG_INFO("Central connected: %s", isCentralConnected() ? "YES" : "NO");
  NRF_LOG_INFO("Peripheral connected: %s", isPeripheralConnected() ? "YES" : "NO");
  NRF_LOG_INFO("Discovered YRobot devices: %d", getDiscoveredDevicesCount());

  // Test discovery functionality
  if (getCentralMode()) {
    NRF_LOG_INFO("Testing YRobot discovery...");
    startYRobotDiscovery();
  }
}

/**@brief Application main function.
 */
int main(void) {
  bool erase_bonds;
  ret_code_t err_code;

  log_init();
  //  NRF_LOG_DEFAULT_BACKENDS_INIT();

#ifdef USE_NRF_DFU
  // Initialize the async SVCI interface to bootloader before any interrupts are enabled.
  err_code = ble_dfu_buttonless_async_svci_init();
  APP_ERROR_CHECK(err_code);
#endif  // USE_NRF_DFU

  timers_init();
  buttons_leds_init(&erase_bonds);

#ifdef USE_UART
  uart_init();
#endif  // USE_UART

  power_management_init();

#ifdef USE_NRF_DFU
  peer_manager_init();
#endif  // USE_NRF_DFU
        // peer_manager_init();

#ifdef USE_FDS
  app_main_board.InitFds();
#endif  // USE_FDS

  // Set central mode - change this to 1 to enable central functionality
  setCentralMode(true);  // Set to 1 to enable both central and peripheral modes

  initBle();
  initSpi();

  nrf_gpio_cfg_output(PIN_SPI_WRITE_INT);
  // app_main_board.enableBleIntPin(true);
  nrf_gpio_cfg_output(PIN_RESET_K66);
  nrf_gpio_pin_write(PIN_RESET_K66, 1);
  app_main_board.init();

  // Start application timers.
  err_code = app_timer_start(sd_timer_id, YR_MICROS_INTERVAL, NULL);
  APP_ERROR_CHECK(err_code);
  err_code = app_timer_start(main_loop_timer_id, YR_MICROS_INTERVAL, NULL);
  APP_ERROR_CHECK(err_code);

  // Enter main loop.
  for (;;) {
    idle_state_handle();
  }
}


/**
 * @}
 */
