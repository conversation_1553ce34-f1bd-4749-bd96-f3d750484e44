#include "yr_util.h"
#include "app_main_board.h"
#include "ble_central_peripheral.h"
#include "nrf_delay.h"
#include "test_data_routing.h"

// NUS Client and GATT Discovery includes
#include "ble_nus_c.h"
#include "ble_db_discovery.h"
#include "nrf_ble_gq.h"

nrf_drv_spis_config_t spis_config;

//----------------------------------------------------------------------------//
//  CENTRAL MODE VARIABLES
//----------------------------------------------------------------------------//
NRF_BLE_SCAN_DEF(m_scan);                                  /**< Scanning module instance. */
static bool is_central = false;                            /**< Flag to enable/disable central mode. */
static char m_target_periph_name[] = "YRobot_FE:8E:36:B5:36:D9";            /**< Name of the device to search for. */
static uint16_t m_central_conn_handle = BLE_CONN_HANDLE_INVALID; /**< Handle for central connection. */

// NUS Client and GATT Discovery variables
BLE_NUS_C_DEF(m_ble_nus_c);                               /**< BLE NUS service client instance. */
BLE_DB_DISCOVERY_DEF(m_db_disc);                          /**< DB discovery module instance. */
static bool m_nus_service_discovered = false;             /**< Flag indicating if NUS service was discovered. */

NRF_BLE_GQ_DEF(m_ble_gatt_queue,                /**< BLE GATT Queue instance. */
               NRF_SDH_BLE_CENTRAL_LINK_COUNT,
               NRF_BLE_GQ_QUEUE_SIZE);
typedef enum {
    PHONE_CENTRAL_PERIPHERAL = 1,     /**< phone <-> peripheral(central) <-> peripheral */
    PHONE_CENTRAL,                    /**< phone <-> peripheral(central) */
    CENTRAL_PERIPHERAL                /**< central <-> peripheral */
} data_route_t;

//----------------------------------------------------------------------------//
//  BLE PERIPHERAL RX
//----------------------------------------------------------------------------//
void nus_data_handler(ble_nus_evt_t *p_evt) {
  if (p_evt->type == BLE_NUS_EVT_RX_DATA) {
    data_route_t data_route = (data_route_t)(p_evt->params.rx_data.p_data[0]);
    bool debug = true;
    if (debug) {
      NRF_LOG_INFO("Received data from phone route:[%d]", data_route);
      NRF_LOG_HEXDUMP_INFO(p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
    }     
    if (p_evt->conn_handle == m_conn_handle) {  // 接收到central的消息
      if (data_route == PHONE_CENTRAL) { // 自己处理
        // app_main_board.handleBleRx((uint8_t *)p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
        sendBluetoothMsgPeripheral((uint8_t *)p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
      } else if (data_route == PHONE_CENTRAL_PERIPHERAL) {  // 需要转发的
        if (is_central) {// 如果是central，就转发给peripheral
          // 先检查Central连接是否准备好
          if (!isCentralReadyForTransmission()) {
            NRF_LOG_ERROR("Central connection not ready for transmission");
            NRF_LOG_INFO("Central handle: 0x%04X, NUS discovered: %s",
                         m_central_conn_handle, m_nus_service_discovered ? "YES" : "NO");
            return;
          }

          NRF_LOG_DEBUG("Forwarding %d bytes from phone to peripheral device", p_evt->params.rx_data.length);
          uint32_t err_code = sendBluetoothMsgCentral((uint8_t *)p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);

          if (err_code == NRF_SUCCESS) {
            if (debug) {
              NRF_LOG_INFO("sent data to device successfully");
            }
          } else {
            NRF_LOG_ERROR("Failed to send data to device: 0x%08X", err_code);

            // 根据错误类型提供建议
            if (err_code == NRF_ERROR_INVALID_STATE) {
              NRF_LOG_WARNING("Suggestion: Check if peripheral device is still connected and NUS service is ready");
            } else if (err_code == NRF_ERROR_RESOURCES) {
              NRF_LOG_WARNING("Suggestion: GATT queue full, try reducing data transmission rate");
            }
          }
        } else {  // 如果是peripheral,就自己处理
          // app_main_board.handleBleRx((uint8_t *)p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
          sendBluetoothMsgPeripheral((uint8_t *)p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
          NRF_LOG_WARNING("Central mode is disabled, processing data locally");
        }
      }

       
    } else if (p_evt->conn_handle == m_central_conn_handle) {
      // Data from other device (central connection)
      NRF_LOG_INFO("Received data from device (central connection)");
      NRF_LOG_HEXDUMP_INFO(p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
      // route_data(DATA_SOURCE_DEVICE, (uint8_t *)p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
    }
  }
}

//----------------------------------------------------------------------------//
//  SPI RX
//----------------------------------------------------------------------------//
void spis_event_handler(nrf_drv_spis_event_t event) {
#ifdef USE_MOCK_TARGET
  return;
#endif  // USE_MOCK_TARGET
  if (event.evt_type == NRF_DRV_SPIS_XFER_DONE) {
    app_main_board.handleSpiRx(event.rx_amount);
  } else if (event.evt_type == NRF_DRV_SPIS_BUFFERS_SET_DONE) {
    // NRF_LOG_INFO("-- SPI BUFFERS SET [%d]", app_main_board.msg_stats[AppMainBoard::SPI_TX].getCount());
    app_main_board.msg_stats[AppMainBoard::SPI_TX].add();
  }
}

uint32_t sendBluetoothMsgProxy(uint8_t *buf, uint16_t len) {
  // return g_send_bluetooth(buf, len);
  return NRF_SUCCESS;
}

/**@brief Function to process data received from connected device.
 *
 * @param[in] data     Pointer to received data.
 * @param[in] length   Length of received data.
 */
static void processDataFromDevice(uint8_t * data, uint16_t length)
{
    NRF_LOG_INFO("Processing data from device, length: %d", length);


    // Process the data through the existing routing logic
    // processDataFromDevice(data, length, DATA_SOURCE_DEVICE);
}

/**@brief Function to send data via central connection.
 */
unsigned int sendBluetoothMsgCentral(unsigned char *buf, unsigned short len) {
  // 详细的状态检查
  if (m_central_conn_handle == BLE_CONN_HANDLE_INVALID) {
    NRF_LOG_ERROR("Central connection handle invalid");
    return NRF_ERROR_INVALID_STATE;
  }

  if (!m_nus_service_discovered) {
    NRF_LOG_ERROR("NUS service not discovered yet");
    return NRF_ERROR_INVALID_STATE;
  }

  // 检查NUS客户端状态
  if (m_ble_nus_c.conn_handle != m_central_conn_handle) {
    NRF_LOG_ERROR("NUS client connection handle mismatch: client=0x%04X, central=0x%04X",
                  m_ble_nus_c.conn_handle, m_central_conn_handle);
    return NRF_ERROR_INVALID_STATE;
  }

  // 检查句柄有效性
  if (m_ble_nus_c.handles.nus_rx_handle == BLE_GATT_HANDLE_INVALID) {
    NRF_LOG_ERROR("NUS RX handle invalid");
    return NRF_ERROR_INVALID_STATE;
  }

  // 检查数据长度
  if (len == 0 || len > 244) {  // 考虑MTU限制
    NRF_LOG_ERROR("Invalid data length: %d", len);
    return NRF_ERROR_INVALID_LENGTH;
  }

  NRF_LOG_DEBUG("Sending %d bytes to central device (handle: 0x%04X)", len, m_central_conn_handle);
  NRF_LOG_HEXDUMP_DEBUG(buf, len);

  uint16_t length = len;
  uint32_t err_code = ble_nus_c_string_send(&m_ble_nus_c, buf, length);

  if (err_code != NRF_SUCCESS) {
    NRF_LOG_ERROR("ble_nus_c_string_send failed: 0x%08X", err_code);
  }

  return err_code;
}

/**@brief Function to check central connection readiness for data transmission.
 */
bool isCentralReadyForTransmission(void) {
    // 基本连接检查
    if (m_central_conn_handle == BLE_CONN_HANDLE_INVALID) {
        NRF_LOG_DEBUG("Central connection handle invalid");
        return false;
    }

    // NUS服务发现检查
    if (!m_nus_service_discovered) {
        NRF_LOG_DEBUG("NUS service not discovered");
        return false;
    }

    // NUS客户端连接句柄检查
    if (m_ble_nus_c.conn_handle != m_central_conn_handle) {
        NRF_LOG_DEBUG("NUS client handle mismatch");
        return false;
    }

    // 句柄有效性检查
    if (m_ble_nus_c.handles.nus_rx_handle == BLE_GATT_HANDLE_INVALID) {
        NRF_LOG_DEBUG("NUS RX handle invalid");
        return false;
    }

    return true;
}

/**@brief Function to send data via peripheral connection.
 */
unsigned int sendBluetoothMsgPeripheral(unsigned char *buf, unsigned short len) {
  if (m_conn_handle != BLE_CONN_HANDLE_INVALID) {
    uint16_t length = len;
    return ble_nus_data_send(&m_nus, buf, &length, m_conn_handle);
  }
  return NRF_ERROR_INVALID_STATE;
}

/**@brief Function to send protocol data to phone.
 */
unsigned int sendDataToPhone(uint8_t data_type, uint8_t *data, uint16_t data_len) {
    if (!isPeripheralConnected()) {
        NRF_LOG_WARNING("Cannot send to phone - not connected");
        return NRF_ERROR_INVALID_STATE;
    }

    uint8_t packet_buf[256];
    // uint16_t packet_len = create_data_packet(packet_buf, data_type, data, data_len);
    uint16_t packet_len = data_len;

    if (packet_len == 0 || packet_len > 244) {  // MTU limit consideration
        NRF_LOG_ERROR("Invalid packet length: %d", packet_len);
        return NRF_ERROR_INVALID_LENGTH;
    }

    return sendBluetoothMsgPeripheral(packet_buf, packet_len);
}

/**@brief Function to send protocol data to device.
 */
unsigned int sendDataToDevice(uint8_t data_type, uint8_t *data, uint16_t data_len) {
    if (!isCentralConnected()) {
        NRF_LOG_WARNING("Cannot send to device - not connected");
        return NRF_ERROR_INVALID_STATE;
    }

    uint8_t packet_buf[256];
    // uint16_t packet_len = create_data_packet(packet_buf, data_type, data, data_len);
    uint16_t packet_len = data_len;

    if (packet_len == 0 || packet_len > 244) {  // MTU limit consideration
        NRF_LOG_ERROR("Invalid packet length: %d", packet_len);
        return NRF_ERROR_INVALID_LENGTH;
    }

    return sendBluetoothMsgCentral(packet_buf, packet_len);
}

/**@brief Function to send heartbeat to all connected devices.
 */
//void sendHeartbeat(void) {
//    uint8_t heartbeat_data[] = {0x01, 0x02, 0x03, 0x04}; // Simple heartbeat payload

//    if (isPeripheralConnected()) {
//        sendDataToPhone(DATA_TYPE_HEARTBEAT, heartbeat_data, sizeof(heartbeat_data));
//        NRF_LOG_INFO("Heartbeat sent to phone");
//    }

//    if (isCentralConnected()) {
//        sendDataToDevice(DATA_TYPE_HEARTBEAT, heartbeat_data, sizeof(heartbeat_data));
//        NRF_LOG_INFO("Heartbeat sent to device");
//    }
//}

/**@brief Function to send status information.
 */
//void sendStatusInfo(void) {
//    // Create status packet with connection information
//    uint8_t status_data[8];
//    status_data[0] = isPeripheralConnected() ? 1 : 0;  // Phone connection status
//    status_data[1] = isCentralConnected() ? 1 : 0;     // Device connection status
//    status_data[2] = is_central ? 1 : 0;               // Central mode enabled
//    status_data[3] = 0;  // Reserved

//    // Add RSSI or other status info if available
//    status_data[4] = 0;  // RSSI placeholder
//    status_data[5] = 0;  // Battery level placeholder
//    status_data[6] = 0;  // Error count placeholder
//    status_data[7] = 0;  // Reserved

//    if (isPeripheralConnected()) {
//        sendDataToPhone(DATA_TYPE_STATUS, status_data, sizeof(status_data));
//    }

//    if (isCentralConnected()) {
//        sendDataToDevice(DATA_TYPE_STATUS, status_data, sizeof(status_data));
//    }

//    NRF_LOG_INFO("Status info sent - Phone: %d, Device: %d, Central: %d",
//                 status_data[0], status_data[1], status_data[2]);
//}

/**@brief Function to check if central connection is active.
 */
int isCentralConnected(void) {
  return (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) ? 1 : 0;
}

/**@brief Function to check if peripheral connection is active.
 */
int isPeripheralConnected(void) {
  return (m_conn_handle != BLE_CONN_HANDLE_INVALID) ? 1 : 0;
}

//----------------------------------------------------------------------------//
//  CENTRAL MODE FUNCTIONS
//----------------------------------------------------------------------------//
 /**@brief Function to check and log BLE state.
 */
static void log_ble_state(void) {
  NRF_LOG_INFO("=== BLE State Check ===");
  NRF_LOG_INFO("Central handle: 0x%04X", m_central_conn_handle);
  NRF_LOG_INFO("Peripheral handle: 0x%04X", m_conn_handle);
  NRF_LOG_INFO("Central mode enabled: %s", is_central ? "YES" : "NO");
  NRF_LOG_INFO("=====================");
}

/**@brief Function for starting the scanning.
 */
static void scan_start(void) {
  ret_code_t err_code;

  // Log current state for debugging
  log_ble_state();

  // Check if we're already connected as central
  if (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) {
    NRF_LOG_WARNING("Already connected as central, not starting scan");
    return;
  }

  NRF_LOG_INFO("Starting scanning for devices named: %s", m_target_periph_name);

  err_code = nrf_ble_scan_start(&m_scan);
  if (err_code == NRF_SUCCESS) {
    NRF_LOG_INFO("Scanning started successfully");
  } else {
    NRF_LOG_ERROR("Failed to start scanning: 0x%x (%s)", err_code,
                  (err_code == NRF_ERROR_INVALID_STATE) ? "INVALID_STATE" :
                  (err_code == NRF_ERROR_BUSY) ? "BUSY" : "OTHER");
  }
}
//----------------------------------------------------------------------------//
//  CONNECTION MANAGEMENT
//----------------------------------------------------------------------------//
static uint32_t m_heartbeat_counter = 0;
static uint32_t m_status_counter = 0;
static uint32_t m_reconnect_counter = 0;

#define HEARTBEAT_INTERVAL_MS 30000    // Send heartbeat every 30 seconds
#define STATUS_INTERVAL_MS 60000       // Send status every 60 seconds
#define RECONNECT_INTERVAL_MS 5000     // Try reconnect every 5 seconds

/**@brief Function to manage periodic tasks.
 */
void manage_periodic_tasks(void) {
    static uint32_t last_tick = 0;
    uint32_t current_tick = app_timer_cnt_get();
    uint32_t elapsed_ms = app_timer_cnt_diff_compute(current_tick, last_tick) / 32.768; // Convert to ms

    if (elapsed_ms < 100) {  // Only run every 100ms
        return;
    }

    last_tick = current_tick;

    // Update counters
    m_heartbeat_counter += elapsed_ms;
    m_status_counter += elapsed_ms;
    m_reconnect_counter += elapsed_ms;

    // Send heartbeat periodically
    if (m_heartbeat_counter >= 3000) {
      uint8_t buffer[20] = {0};
      for (uint8_t i = 0; i < 20; i++) {
        if (is_central) {
          buffer[i] = PHONE_CENTRAL;
        } else {
          buffer[i] = PHONE_CENTRAL_PERIPHERAL;
        }
      }
      if (isPeripheralConnected()) {
        sendBluetoothMsgPeripheral(buffer, sizeof(buffer));           
      }
      m_heartbeat_counter = 0;
    }

    // // Send status periodically
    // if (m_status_counter >= STATUS_INTERVAL_MS) {
    //     sendStatusInfo();
    //     m_status_counter = 0;
    // }

    // // Try to reconnect if central mode is enabled but not connected
    // if (is_central && !isCentralConnected() && m_reconnect_counter >= RECONNECT_INTERVAL_MS) {
    //     NRF_LOG_INFO("Attempting to reconnect in central mode");
    //     scan_start();
    //     m_reconnect_counter = 0;
    // }
}

/**@brief Function to handle connection quality monitoring.
 */
void monitor_connection_quality(void) {
    // Monitor RSSI and connection parameters
    if (isPeripheralConnected()) {
        // Could add RSSI monitoring for peripheral connection
    }

    if (isCentralConnected()) {
        // Could add RSSI monitoring for central connection
    }
}

/**@brief Function to handle graceful disconnection.
 */
void disconnect_all_connections(void) {
    if (isPeripheralConnected()) {
        NRF_LOG_INFO("Disconnecting peripheral connection");
        sd_ble_gap_disconnect(m_conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
    }

    if (isCentralConnected()) {
        NRF_LOG_INFO("Disconnecting central connection");
        sd_ble_gap_disconnect(m_central_conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
    }
}

void idle_state_handle(void) {
  app_main_board.update();
  manage_periodic_tasks();
  // monitor_connection_quality();
  // check_test_schedule();  // Check if it's time to run tests
}

void timer_main_loop_handler(void *p_context) {
    // This can be used for more precise timing if needed
}

/**@brief Function for handling Scanning Module events.
 */
static void scan_evt_handler(scan_evt_t const * p_scan_evt) {
  ret_code_t err_code;

  switch(p_scan_evt->scan_evt_id) {
    case NRF_BLE_SCAN_EVT_FILTER_MATCH: {
      ble_gap_evt_adv_report_t const * p_adv = p_scan_evt->params.filter_match.p_adv_report;
      ble_gap_scan_params_t const * p_scan_param = p_scan_evt->p_scan_params;

      NRF_LOG_INFO("Found device: %s ", m_target_periph_name);
      NRF_LOG_INFO("MAC: %02X:%02X:%02X:%02X:%02X:%02X",
          p_adv->peer_addr.addr[5], p_adv->peer_addr.addr[4], p_adv->peer_addr.addr[3],
          p_adv->peer_addr.addr[2], p_adv->peer_addr.addr[1], p_adv->peer_addr.addr[0]);
      NRF_LOG_INFO("RSSI: %d dBm, Data len: %d", p_adv->rssi, p_adv->data.len);
      // NRF_LOG_INFO("Address type: %d", p_adv->peer_addr.addr_type);

      // Check if we're already connected or connecting
      if (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) {
        NRF_LOG_WARNING("Already connected as central (handle: 0x%04X), disconnecting first", m_central_conn_handle);
        // Disconnect existing central connection to make room for new one
        sd_ble_gap_disconnect(m_central_conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
        // nrf_delay_ms(100);
        m_central_conn_handle = BLE_CONN_HANDLE_INVALID;
      }

      // Also check peripheral connection - we might need to free up resources
      if (m_conn_handle != BLE_CONN_HANDLE_INVALID) {
        NRF_LOG_INFO("Peripheral connection active (handle: 0x%04X), keeping it", m_conn_handle);
      }

      // Check signal strength - skip if too weak
      if (p_adv->rssi < -80) {
        NRF_LOG_WARNING("Signal too weak (RSSI: %d), skipping connection", p_adv->rssi);
        return;
      }

      // Check if device is connectable by examining advertising type
      NRF_LOG_INFO("Adv type: 0x%02X, Connectable: %s",
                   p_adv->type.connectable,
                   p_adv->type.connectable ? "YES" : "NO");

      if (!p_adv->type.connectable) {
        NRF_LOG_WARNING("Device is not connectable, skipping");
        return;
      }

      // Stop scanning before attempting connection - use direct SoftDevice call
      ret_code_t stop_err = sd_ble_gap_scan_stop();
      NRF_LOG_INFO("Stopped scanning (result: 0x%x), attempting connection...", stop_err);

      // Use very conservative connection parameters that most devices accept
      ble_gap_conn_params_t conn_param = {
        .min_conn_interval = CONN_INTERVAL_DEFAULT,    // 15ms
        .max_conn_interval = CONN_INTERVAL_DEFAULT,    // 15ms
        .slave_latency = 0,
        .conn_sup_timeout = CONN_SUP_TIMEOUT       // 4s
      };

      NRF_LOG_INFO("Connection params: min=%d, max=%d, latency=%d, timeout=%d",
                   conn_param.min_conn_interval, conn_param.max_conn_interval,
                   conn_param.slave_latency, conn_param.conn_sup_timeout);

      // Create a copy of the peer address and ensure correct address type
      ble_gap_addr_t peer_addr = p_adv->peer_addr;

      // Log address type for debugging
      NRF_LOG_INFO("Original addr type: %d", peer_addr.addr_type);

      // Try to fix address type if it seems wrong
      if (peer_addr.addr_type > BLE_GAP_ADDR_TYPE_RANDOM_PRIVATE_NON_RESOLVABLE) {
        NRF_LOG_WARNING("Invalid address type %d, setting to PUBLIC", peer_addr.addr_type);
        peer_addr.addr_type = BLE_GAP_ADDR_TYPE_PUBLIC;
      }

      // Try connection with custom parameters first
      NRF_LOG_INFO("Attempting connection with custom parameters...");
      err_code = sd_ble_gap_connect(&peer_addr,
                                    // &simple_scan_params,
                                    p_scan_param,
                                    &conn_param,
                                    APP_BLE_CONN_CFG_TAG);

      if (err_code == NRF_SUCCESS) {
        NRF_LOG_INFO("Connection request sent successfully");
      } else {
        NRF_LOG_ERROR("sd_ble_gap_connect() with custom params failed: 0x%x", err_code);
        scan_start();
      }

    } break;

    case NRF_BLE_SCAN_EVT_NOT_FOUND: {
      NRF_LOG_DEBUG("No matching device found during scan.");
    } break;

    default:
      break;
  }
}

/**@brief Function for handling the Nordic UART Service Client events.
 *
 * @details This function will be called for all NUS events which are passed to
 *          the application.
 *
 * @param[in] p_ble_nus_c   NUS Client Handle.
 * @param[in] p_ble_nus_evt Pointer to the NUS Client event.
 */
static void ble_nus_c_evt_handler(ble_nus_c_t * p_ble_nus_c, ble_nus_c_evt_t const * p_ble_nus_evt)
{
    ret_code_t err_code;

    switch (p_ble_nus_evt->evt_type)
    {
        case BLE_NUS_C_EVT_DISCOVERY_COMPLETE:
            // Prevent duplicate processing
            if (m_nus_service_discovered)
            {
                NRF_LOG_DEBUG("NUS service already discovered, ignoring duplicate event");
                break;
            }

            NRF_LOG_INFO("NUS service discovered on the server.");

            // Log discovered handles
            NRF_LOG_INFO("NUS RX handle: 0x%04X", p_ble_nus_evt->handles.nus_rx_handle);
            NRF_LOG_INFO("NUS TX handle: 0x%04X", p_ble_nus_evt->handles.nus_tx_handle);
            NRF_LOG_INFO("NUS TX CCCD handle: 0x%04X", p_ble_nus_evt->handles.nus_tx_cccd_handle);

            // Assign connection handle to NUS client
            err_code = ble_nus_c_handles_assign(p_ble_nus_c, p_ble_nus_evt->conn_handle, &p_ble_nus_evt->handles);
            if (err_code != NRF_SUCCESS)
            {
                NRF_LOG_ERROR("Failed to assign handles: 0x%X", err_code);
                break;
            }

            // Verify connection handle and CCCD handle before enabling notifications
            NRF_LOG_INFO("Connection handle: 0x%04X", p_ble_nus_c->conn_handle);
            NRF_LOG_INFO("Stored TX CCCD handle: 0x%04X", p_ble_nus_c->handles.nus_tx_cccd_handle);

            if (p_ble_nus_c->handles.nus_tx_cccd_handle == BLE_GATT_HANDLE_INVALID)
            {
                NRF_LOG_ERROR("TX CCCD handle is invalid, cannot enable notifications");
                m_nus_service_discovered = true; // Still mark as discovered for data sending
                break;
            }

            // Enable TX notifications
            NRF_LOG_INFO("Enabling TX notifications...");
            err_code = ble_nus_c_tx_notif_enable(p_ble_nus_c);
            if (err_code != NRF_SUCCESS)
            {
                NRF_LOG_ERROR("Failed to enable TX notifications: 0x%X", err_code);
                // Don't break here, still mark service as discovered
            }
            else
            {
                NRF_LOG_INFO("TX notifications enabled successfully");
            }

            m_nus_service_discovered = true;
            NRF_LOG_INFO("Connected to device with Nordic UART Service.");
            break;

        case BLE_NUS_C_EVT_NUS_TX_EVT: {
            // Data received from peripheral device
            bool debug = true;
            if (debug) {
              NRF_LOG_INFO("Received data from NUS TX characteristic.");
              // NRF_LOG_HEXDUMP_INFO(p_ble_nus_evt->p_data, p_ble_nus_evt->data_len);              
            }
            data_route_t data_route = (data_route_t)(p_ble_nus_evt->p_data[0]);
            if (data_route == CENTRAL_PERIPHERAL) { // 自己处理
              app_main_board.handleBleRx((uint8_t *)p_ble_nus_evt->p_data, p_ble_nus_evt->data_len);
            } else if(data_route == PHONE_CENTRAL_PERIPHERAL) { // 转发给phone
              uint32_t err_code = sendBluetoothMsgPeripheral(p_ble_nus_evt->p_data, p_ble_nus_evt->data_len);
              if (debug) {
                if (err_code == NRF_SUCCESS) {
                    NRF_LOG_INFO("data sent to phone successfully");
                } else {
                    NRF_LOG_ERROR("Failed to send data to phone: 0x%08X", err_code);
                }                
              }
            }
        }  break;

        case BLE_NUS_C_EVT_DISCONNECTED:
            NRF_LOG_INFO("Disconnected from device with Nordic UART Service.");
            m_nus_service_discovered = false;
            break;
    }
}

/**@brief Function for handling database discovery events.
 *
 * @details This function is a callback function to handle events from the database discovery module.
 *          Depending on the UUIDs that are discovered, this function should forward the events
 *          to their respective services.
 *
 * @param[in] p_event  Pointer to the database discovery event.
 */
static void db_disc_handler(ble_db_discovery_evt_t * p_evt)
{
    NRF_LOG_INFO("Database discovery event: type=%d, conn_handle=0x%04X", p_evt->evt_type, p_evt->conn_handle);

    switch (p_evt->evt_type)
    {
        case BLE_DB_DISCOVERY_COMPLETE:
            NRF_LOG_INFO("Database discovery complete for connection 0x%04X", p_evt->conn_handle);
            break;

        case BLE_DB_DISCOVERY_ERROR:
            NRF_LOG_ERROR("Database discovery error for connection 0x%04X", p_evt->conn_handle);
            break;

        case BLE_DB_DISCOVERY_SRV_NOT_FOUND:
            NRF_LOG_WARNING("Service not found during discovery for connection 0x%04X", p_evt->conn_handle);
            break;

        case BLE_DB_DISCOVERY_AVAILABLE:
            NRF_LOG_INFO("Database discovery available for connection 0x%04X", p_evt->conn_handle);
            break;

        default:
            NRF_LOG_DEBUG("Unknown database discovery event: %d", p_evt->evt_type);
            break;
    }

    // Forward the event to NUS Client
    ble_nus_c_on_db_disc_evt(&m_ble_nus_c, p_evt);
}

/**@brief Function for handling NUS Client errors.
 */
static void nus_c_error_handler(uint32_t nrf_error)
{
    NRF_LOG_ERROR("NUS Client error: 0x%X", nrf_error);

    // 详细的错误分析
    switch (nrf_error) {
        case NRF_ERROR_INVALID_STATE:
            NRF_LOG_ERROR("Error: Invalid state - connection may be unstable or GATT operation in progress");
            NRF_LOG_INFO("Central connection handle: 0x%04X", m_central_conn_handle);
            NRF_LOG_INFO("NUS service discovered: %s", m_nus_service_discovered ? "YES" : "NO");
            NRF_LOG_INFO("NUS client connection handle: 0x%04X", m_ble_nus_c.conn_handle);
            break;

        case NRF_ERROR_RESOURCES:
            NRF_LOG_ERROR("Error: No resources - GATT queue may be full");
            break;

        case NRF_ERROR_NOT_FOUND:
            NRF_LOG_ERROR("Error: Not found - characteristic or service not available");
            break;

        case NRF_ERROR_BUSY:
            NRF_LOG_ERROR("Error: Busy - another GATT operation in progress");
            break;

        case NRF_ERROR_TIMEOUT:
            NRF_LOG_ERROR("Error: Timeout - GATT operation timed out");
            break;

        default:
            NRF_LOG_ERROR("Error: Unknown error code 0x%X", nrf_error);
            break;
    }

    // 尝试恢复措施
    if (nrf_error == NRF_ERROR_INVALID_STATE) {
        NRF_LOG_INFO("Attempting to recover from invalid state...");
        // 可以在这里添加重连逻辑或重新初始化
    }
}

/**@brief Function for initializing the NUS Client and GATT Discovery.
 */
static void nus_c_init(void)
{
    ret_code_t       err_code;
    ble_nus_c_init_t init;

    init.evt_handler   = ble_nus_c_evt_handler;
    init.error_handler = nus_c_error_handler;
    init.p_gatt_queue  = &m_ble_gatt_queue;

    NRF_LOG_INFO("Initializing NUS Client...");
    err_code = ble_nus_c_init(&m_ble_nus_c, &init);
    APP_ERROR_CHECK(err_code);
    NRF_LOG_INFO("NUS Client initialized successfully");
}

/**@brief Function for initializing the database discovery module.
 */
static void db_discovery_init(void)
{
    ble_db_discovery_init_t db_init;

    memset(&db_init, 0, sizeof(ble_db_discovery_init_t));

    db_init.evt_handler  = db_disc_handler;
    db_init.p_gatt_queue = &m_ble_gatt_queue;

    NRF_LOG_INFO("Initializing Database Discovery...");
    ret_code_t err_code = ble_db_discovery_init(&db_init);
    APP_ERROR_CHECK(err_code);
    NRF_LOG_INFO("Database Discovery initialized successfully");
}

/**@brief Function for initialization the scanning and setting the filters.
 */
static void scan_init(void) {
  ret_code_t err_code;
  nrf_ble_scan_init_t init_scan;

  memset(&init_scan, 0, sizeof(init_scan));
  init_scan.connect_if_match = false;
  init_scan.conn_cfg_tag     = APP_BLE_CONN_CFG_TAG;

  err_code = nrf_ble_scan_init(&m_scan, &init_scan, scan_evt_handler);
  APP_ERROR_CHECK(err_code);

  // Set filter to scan for devices with specific name
  err_code = nrf_ble_scan_filter_set(&m_scan,
                                     SCAN_NAME_FILTER,
                                     m_target_periph_name);
  APP_ERROR_CHECK(err_code);

  // Enable name filter
  err_code = nrf_ble_scan_filters_enable(&m_scan,
                                         NRF_BLE_SCAN_NAME_FILTER,
                                         false);
  APP_ERROR_CHECK(err_code);

  NRF_LOG_INFO("Scan initialized with filters for: %s", m_target_periph_name);
}

void handleConnection(bool state) {
  if (state) {
    g_time_connected = millis();
    app_main_board.handleConnected();
  } else {
    app_main_board.handleDisconnected();
  }
}

#ifdef __cplusplus
extern "C" {
#endif

static void ble_evt_handler(ble_evt_t const *p_ble_evt, void *p_context) {
  uint32_t err_code;
  uint32_t count = 0;

  ble_gap_evt_t const *p_gap_evt = &p_ble_evt->evt.gap_evt;

  uint16_t conn_handle = p_ble_evt->evt.gap_evt.conn_handle;
  uint8_t role = p_ble_evt->evt.gap_evt.params.connected.role;
  // NRF_LOG_INFO("\t\tble_evt_handler [%d]", p_ble_evt->header.evt_id);

  switch (p_ble_evt->header.evt_id) {
    case BLE_GATTS_EVT_HVN_TX_COMPLETE:
      if (role == BLE_GAP_ROLE_PERIPH) {
        count = p_ble_evt->evt.gatts_evt.params.hvn_tx_complete.count;
        app_main_board.handleTxComplete(count);
        // NRF_LOG_INFO("BLE TX Complete. N:%d",count)        
      } else if(role == BLE_GAP_ROLE_CENTRAL) {

      } 
      break;

    case BLE_GAP_EVT_CONNECTED: {
      if (role == BLE_GAP_ROLE_PERIPH) {
        NRF_LOG_INFO("BLE Connected as Peripheral");
        m_conn_handle = conn_handle;
        err_code = nrf_ble_qwr_conn_handle_assign(&m_qwr, m_conn_handle);
        APP_ERROR_CHECK(err_code);
        err_code = bsp_indication_set(BSP_INDICATE_CONNECTED);
        APP_ERROR_CHECK(err_code);
        handleConnection(true);
        // err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_CONN, conn_handle, 4);
        // APP_ERROR_CHECK(err_code);
        err_code = sd_ble_gap_phy_update(p_gap_evt->conn_handle, &m_test_params.phys);
        APP_ERROR_CHECK(err_code);
        // err_code = nrf_ble_gatt_data_length_set(&m_gatt, BLE_CONN_HANDLE_INVALID, 251);
        // APP_ERROR_CHECK(err_code);
      } else if (role == BLE_GAP_ROLE_CENTRAL) {
        NRF_LOG_INFO("BLE Connected as Central");
        m_central_conn_handle = conn_handle;
        // Stop scanning when connected as central
        nrf_ble_scan_stop();

        // Start GATT service discovery
        NRF_LOG_INFO("Starting GATT service discovery...");
        err_code = ble_db_discovery_start(&m_db_disc, conn_handle);
        APP_ERROR_CHECK(err_code);
      }
    } break;

    case BLE_GAP_EVT_DISCONNECTED: {
      uint16_t conn_handle = p_ble_evt->evt.gap_evt.conn_handle;

      NRF_LOG_INFO("BLE Disconnected");
      NRF_LOG_INFO("BLE Disconnect reason %d", p_ble_evt->evt.gap_evt.params.disconnected.reason);

      // Check which connection was disconnected
      if (conn_handle == m_conn_handle) {
        NRF_LOG_INFO("Peripheral connection disconnected");
        m_conn_handle = BLE_CONN_HANDLE_INVALID;
        // LED indication will be changed when advertising starts.
        handleConnection(false);        
      } else if (conn_handle == m_central_conn_handle) {
        NRF_LOG_INFO("Central connection disconnected");
        m_central_conn_handle = BLE_CONN_HANDLE_INVALID;

        // Reset NUS service discovery state
        m_nus_service_discovered = false;

        // Restart scanning if central mode is enabled
        if (is_central) {
          scan_start();
        }
      }
    } break;

    case BLE_GAP_EVT_PHY_UPDATE_REQUEST: {
      NRF_LOG_INFO("PHY update request.");
      // ble_gap_phys_t phys = {
      //   //        .rx_phys = BLE_GAP_PHY_AUTO,
      //   //        .tx_phys = BLE_GAP_PHY_AUTO,
      // };
      // phys.rx_phys = BLE_GAP_PHY_AUTO;
      // phys.tx_phys = BLE_GAP_PHY_AUTO;
      // err_code = sd_ble_gap_phy_update(p_ble_evt->evt.gap_evt.conn_handle, &phys);
      ble_gap_evt_t const *p_gap_evt = &p_ble_evt->evt.gap_evt;
      err_code = sd_ble_gap_phy_update(p_gap_evt->conn_handle, &m_test_params.phys);
      APP_ERROR_CHECK(err_code);
    } break;
    case BLE_GAP_EVT_SEC_PARAMS_REQUEST:
      NRF_LOG_INFO("BLE_GAP_EVT_SEC_PARAMS_REQUEST");
      // Pairing not supported
      err_code = sd_ble_gap_sec_params_reply(m_conn_handle, BLE_GAP_SEC_STATUS_PAIRING_NOT_SUPP, NULL, NULL);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GATTS_EVT_SYS_ATTR_MISSING:
      NRF_LOG_INFO("GATTS_EVT_SYS_ATTR_MISSING");
      // No system attributes have been stored.
      err_code = sd_ble_gatts_sys_attr_set(m_conn_handle, NULL, 0, 0);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GATTC_EVT_TIMEOUT:
      // Disconnect on GATT Client timeout event.
      NRF_LOG_INFO("GATT Client Timeout.");
      err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gattc_evt.conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
      APP_ERROR_CHECK(err_code);
      break;

    case BLE_GAP_EVT_ADV_REPORT:
      // This event is handled by the scanning module
      break;

    case BLE_GAP_EVT_TIMEOUT:
      NRF_LOG_INFO("BLE_GAP_EVT_TIMEOUT");
      // if (p_gap_evt->params.timeout.src == BLE_GAP_TIMEOUT_SRC_ADVERTISING) {
      //   NRF_LOG_INFO("Advertising timeout");
      // } else 
      if (p_gap_evt->params.timeout.src == BLE_GAP_TIMEOUT_SRC_CONN) {
        NRF_LOG_INFO("Connection timeout - restarting scan");
        // Connection timeout, restart scanning if central mode is enabled
        if (is_central) {
          scan_start();
        }
      } else if (p_gap_evt->params.timeout.src == BLE_GAP_TIMEOUT_SRC_SCAN) {
        NRF_LOG_INFO("Scan timeout - restarting scan");
        if (is_central) {
          scan_start();
        }
      }
      break;

    case BLE_GAP_EVT_CONN_PARAM_UPDATE_REQUEST: {
      // Accept connection parameter update request
      err_code = sd_ble_gap_conn_param_update(p_gap_evt->conn_handle,
                                              &p_gap_evt->params.conn_param_update_request.conn_params);
      APP_ERROR_CHECK(err_code);
      NRF_LOG_INFO("Connection parameters updated");
    } break;

    case BLE_GATTS_EVT_TIMEOUT:
      // Disconnect on GATT Server timeout event.
      NRF_LOG_INFO("GATT Server Timeout.");
      err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gatts_evt.conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GAP_EVT_AUTH_KEY_REQUEST:
      NRF_LOG_INFO("BLE_GAP_EVT_AUTH_KEY_REQUEST");
      break;
    case BLE_GAP_EVT_LESC_DHKEY_REQUEST:
      NRF_LOG_INFO("BLE_GAP_EVT_LESC_DHKEY_REQUEST");
      break;
    case BLE_GAP_EVT_AUTH_STATUS:
      NRF_LOG_INFO("BLE_GAP_EVT_AUTH_STATUS: status=0x%x bond=0x%x lv4: %d kdist_own:0x%x kdist_peer:0x%x",
                   p_ble_evt->evt.gap_evt.params.auth_status.auth_status,
                   p_ble_evt->evt.gap_evt.params.auth_status.bonded,
                   p_ble_evt->evt.gap_evt.params.auth_status.sm1_levels.lv4,
                   *((uint8_t *)&p_ble_evt->evt.gap_evt.params.auth_status.kdist_own),
                   *((uint8_t *)&p_ble_evt->evt.gap_evt.params.auth_status.kdist_peer));
      break;
      
      case BLE_GATTC_EVT_PRIM_SRVC_DISC_RSP:
          //
          break;

      case BLE_GATTC_EVT_CHAR_DISC_RSP:
          //
          break;

      case BLE_GATTC_EVT_HVX:

          break;     
    default:
      // No implementation needed.
      break;
  }

  // Note: All BLE events are automatically handled by the respective DEF macros:
  // - BLE_NUS_C_DEF automatically handles NUS Client events
  // - BLE_DB_DISCOVERY_DEF automatically handles Database Discovery events
  // - NRF_BLE_GQ_DEF automatically handles GATT Queue events
}


/**@brief Function for the SoftDevice initialization.
 *
 * @details This function initializes the SoftDevice and the BLE event interrupt.
 */
void ble_stack_init(void) {
  ret_code_t err_code;

  NRF_LOG_INFO("ble_stack_init");

  err_code = nrf_sdh_enable_request();
  APP_ERROR_CHECK(err_code);

  // Configure the BLE stack using the default settings.
  // Fetch the start address of the application RAM.
  uint32_t ram_start = 0;
  err_code = nrf_sdh_ble_default_cfg_set(APP_BLE_CONN_CFG_TAG, &ram_start);
  if (err_code == NRF_ERROR_NO_MEM) {
    NRF_LOG_WARNING("Insufficient RAM for SoftDevice. Required RAM start: 0x%08X", ram_start);
  }
  APP_ERROR_CHECK(err_code);

  ble_cfg_t ble_cfg;
  memset(&ble_cfg, 0, sizeof(ble_cfg));
  ble_cfg.conn_cfg.conn_cfg_tag = APP_BLE_CONN_CFG_TAG;
  // ble_cfg.conn_cfg.params.gattc_conn_cfg.write_cmd_tx_queue_size = 10;
  // ble_cfg.conn_cfg.params.gattc_conn_cfg.write_cmd_tx_queue_size = 8;
  // ble_cfg.conn_cfg.params.gatts_conn_cfg.hvn_tx_queue_size = 10;
  // ble_cfg.conn_cfg.params.gatts_conn_cfg.hvn_tx_queue_size = 7;
  err_code = sd_ble_cfg_set(BLE_CONN_CFG_GATTC, &ble_cfg, ram_start);
  APP_ERROR_CHECK(err_code);

  // Enable BLE stack.
  err_code = nrf_sdh_ble_enable(&ram_start);
  if (err_code == NRF_ERROR_NO_MEM) {
    NRF_LOG_ERROR("Insufficient RAM for SoftDevice. Required RAM start: 0x%08X", ram_start);
    NRF_LOG_ERROR("Please update your linker script to set RAM start to: 0x%08X", ram_start);
    NRF_LOG_ERROR("Current RAM start is probably: 0x200031D8");
    NRF_LOG_ERROR("Required RAM start should be: 0x%08X", ram_start);
  }
  APP_ERROR_CHECK(err_code);

  // ble_opt_t gap_opt;
  // gap_opt.gap_opt.ext_len.rxtx_max_pdu_payload_size = 251;  // Example: set max length to 54bytes
  // err_code = sd_ble_opt_set(BLE_GAP_OPT_EXT_LEN, &gap_opt);
  // APP_ERROR_CHECK(err_code);

  // Register a handler for BLE events.
  NRF_SDH_BLE_OBSERVER(m_ble_observer, APP_BLE_OBSERVER_PRIO, ble_evt_handler, NULL);
}

#ifdef __cplusplus
}
#endif

bool initSpi(void) {
  spis_config.csn_pin = SPIS_CSN_PIN;
  spis_config.sck_pin = SPIS_SCK_PIN;
  spis_config.mosi_pin = SPIS_MOSI_PIN;
  spis_config.miso_pin = SPIS_MISO_PIN;
  spis_config.mode = NRF_SPIS_MODE_0;
  spis_config.bit_order = NRF_SPIS_BIT_ORDER_MSB_FIRST;
  spis_config.csn_pullup = NRFX_SPIS_DEFAULT_CSN_PULLUP;
  spis_config.miso_drive = NRFX_SPIS_DEFAULT_MISO_DRIVE;
  spis_config.def = NRFX_SPIS_DEFAULT_DEF;
  spis_config.orc = NRFX_SPIS_DEFAULT_ORC;
  spis_config.irq_priority = NRFX_SPIS_DEFAULT_CONFIG_IRQ_PRIORITY;

  APP_ERROR_CHECK(nrf_drv_spis_init(&spis, &spis_config, spis_event_handler));
  NRF_LOG_INFO("Init SPIS");
  return true;
}

bool initBle(void) {
  bool erase_bonds = false;
  ret_code_t err_code;

  ble_stack_init();
  gap_params_init();
  gatt_init();
  services_init();

  // Initialize client services for central mode
  db_discovery_init();
  nus_c_init();

  advertising_init();
  conn_params_init();

  gatt_mtu_set(m_test_params.att_mtu);
  conn_evt_len_ext_set(m_test_params.conn_evt_len_ext_enabled);

  print_ble_config();

  // sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_CONN, BLE_CONN_HANDLE_INVALID, transmitPower);
  err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_ADV, m_advertising.adv_handle, 4);
  APP_ERROR_CHECK(err_code);
  // err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_ADV, BLE_CONN_HANDLE_INVALID, 1);

  // Start execution.
  NRF_LOG_INFO("\r\nUART started.");
  NRF_LOG_INFO("Debug logging for UART over RTT started.");

  // Initialize and start central mode if enabled
  if (is_central) {
    NRF_LOG_INFO("Initializing Central mode");

    // 需要配对
    if (app_main_board.IsPairedDeviceSerialNumberValid()) {
      const char* paired_serial = app_main_board.GetPairedDeviceSerialNumber();
      NRF_LOG_INFO("Found valid paired device serial number: [%s]", paired_serial);

      //
      memset(m_target_periph_name, 0, sizeof(m_target_periph_name));
      // memcpy(m_target_periph_name, paired_serial, strlen(paired_serial));
      snprintf(m_target_periph_name, sizeof(m_target_periph_name), "YRobot_%s", paired_serial);

      NRF_LOG_INFO("Updated target device name to: [%s]", m_target_periph_name);

      scan_init();
      scan_start();
    } else {
      NRF_LOG_WARNING("No valid paired device serial number found (all 0xFF)");
      NRF_LOG_WARNING("Central mode will not scan until a device is paired");
      // 没有配对的设备，只初始化，不扫描
      scan_init();
    }
  } else {
    NRF_LOG_INFO("Central mode disabled");
  }

  // Always start advertising (peripheral mode)
  NRF_LOG_INFO("Starting Peripheral mode (advertising)");
  advertising_start(erase_bonds);

  return true;
}

/**@brief Function to enable/disable central mode.
 */
void setCentralMode(int enable) {
  is_central = (enable != 0);
  NRF_LOG_INFO("Central mode %s", is_central ? "ENABLED" : "DISABLED");
}

/**@brief Function to get current central mode status.
 */
int getCentralMode(void) {
  return is_central ? 1 : 0;
}

/**@brief Test function to demonstrate central/peripheral functionality.
 */
void testCentralPeripheralMode(void) {
  static uint32_t test_counter = 0;
  test_counter++;

  NRF_LOG_INFO("=== Central/Peripheral Test #%d ===", test_counter);
  NRF_LOG_INFO("Central mode: %s", getCentralMode() ? "ENABLED" : "DISABLED");
  NRF_LOG_INFO("Central connected: %s", isCentralConnected() ? "YES" : "NO");
  NRF_LOG_INFO("Peripheral connected: %s", isPeripheralConnected() ? "YES" : "NO");

  // Test sending data if connections are active
  if (isCentralConnected()) {
    uint8_t test_data[] = "Test from Central";
    uint32_t result = sendBluetoothMsgCentral(test_data, sizeof(test_data) - 1);
    NRF_LOG_INFO("Central TX result: 0x%X", result);
  }

  if (isPeripheralConnected()) {
    uint8_t test_data[] = "Test from Peripheral";
    uint32_t result = sendBluetoothMsgProxy(test_data, sizeof(test_data) - 1);
    NRF_LOG_INFO("Peripheral TX result: 0x%X", result);
  }

  NRF_LOG_INFO("=== Test Complete ===");
}

// /**@brief Test function for data routing functionality.
//  */
// void testDataRouting(void) {
//     static uint32_t test_counter = 0;
//     test_counter++;

//     NRF_LOG_INFO("=== Data Routing Test #%d ===", test_counter);

//     // Test 1: Send data to phone if connected
//     if (isPeripheralConnected()) {
//         uint8_t test_data[] = "Hello from device!";
//         uint32_t err_code = sendDataToPhone(DATA_TYPE_LOCAL_PROCESS, test_data, simple_strlen((char*)test_data));
//         if (err_code == NRF_SUCCESS) {
//             NRF_LOG_INFO("Test data sent to phone successfully");
//         } else {
//             NRF_LOG_ERROR("Failed to send test data to phone: 0x%08X", err_code);
//         }
//     }

//     // Test 2: Send data to device if connected
//     if (isCentralConnected()) {
//         uint8_t test_data[] = "Hello from central!";
//         uint32_t err_code = sendDataToDevice(DATA_TYPE_LOCAL_PROCESS, test_data, simple_strlen((char*)test_data));
//         if (err_code == NRF_SUCCESS) {
//             NRF_LOG_INFO("Test data sent to device successfully");
//         } else {
//             NRF_LOG_ERROR("Failed to send test data to device: 0x%08X", err_code);
//         }
//     }

//     // Test 3: Send heartbeat
//     sendHeartbeat();

//     // Test 4: Send status
//     sendStatusInfo();

//     NRF_LOG_INFO("=== Data Routing Test Complete ===");
// }

// /**@brief Function to demonstrate forwarding functionality.
//  */
// void testForwarding(void) {
//     NRF_LOG_INFO("=== Testing Data Forwarding ===");

//     // Test forwarding from phone to device
//     if (isPeripheralConnected() && isCentralConnected()) {
//         uint8_t forward_data[] = "Forward this to device";
//         uint32_t err_code = sendDataToDevice(DATA_TYPE_FORWARD_TO_DEVICE, forward_data, simple_strlen((char*)forward_data));
//         if (err_code == NRF_SUCCESS) {
//             NRF_LOG_INFO("Forward test: Phone -> Device successful");
//         } else {
//             NRF_LOG_ERROR("Forward test: Phone -> Device failed: 0x%08X", err_code);
//         }

//         // Test forwarding from device to phone
//         uint8_t forward_data2[] = "Forward this to phone";
//         err_code = sendDataToPhone(DATA_TYPE_FORWARD_TO_PHONE, forward_data2, simple_strlen((char*)forward_data2));
//         if (err_code == NRF_SUCCESS) {
//             NRF_LOG_INFO("Forward test: Device -> Phone successful");
//         } else {
//             NRF_LOG_ERROR("Forward test: Device -> Phone failed: 0x%08X", err_code);
//         }
//     } else {
//         NRF_LOG_WARNING("Cannot test forwarding - need both connections active");
//         NRF_LOG_INFO("Phone connected: %s, Device connected: %s",
//                      isPeripheralConnected() ? "YES" : "NO",
//                      isCentralConnected() ? "YES" : "NO");
//     }

//     NRF_LOG_INFO("=== Forwarding Test Complete ===");
// }

void enableNotify(uint16_t conn_handle, uint16_t cccd_handle)
{
    // uint8_t cccd_val[2] = {BLE_GATT_HVX_NOTIFICATION, 0};
    // ble_gattc_write_params_t write_params = {
    //     .handle   = cccd_handle,
    //     .len      = 2,
    //     .p_value  = cccd_val,
    //     .write_op = BLE_GATT_OP_WRITE_REQ,
    //     .offset   = 0
    // };
    // sd_ble_gattc_write(conn_handle, &write_params);
}

/**@brief Application main function.
 */
int main(void) {
  bool erase_bonds;
  ret_code_t err_code;

  log_init();
  //  NRF_LOG_DEFAULT_BACKENDS_INIT();

#ifdef USE_NRF_DFU
  // Initialize the async SVCI interface to bootloader before any interrupts are enabled.
  err_code = ble_dfu_buttonless_async_svci_init();
  APP_ERROR_CHECK(err_code);
#endif  // USE_NRF_DFU

  timers_init();
  buttons_leds_init(&erase_bonds);

#ifdef USE_UART
  uart_init();
#endif  // USE_UART

  power_management_init();

#ifdef USE_NRF_DFU
  peer_manager_init();
#endif  // USE_NRF_DFU
        // peer_manager_init();

#ifdef USE_FDS
  app_main_board.InitFds();
#endif  // USE_FDS

  // Set central mode - change this to 1 to enable central functionality
  bool central_mode = app_main_board.IsPairedDeviceSerialNumberValid();
  setCentralMode(central_mode);  // Set to 1 to enable both central and peripheral modes

  initBle();
  initSpi();

  nrf_gpio_cfg_output(PIN_SPI_WRITE_INT);
  // app_main_board.enableBleIntPin(true);
  nrf_gpio_cfg_output(PIN_RESET_K66);
  nrf_gpio_pin_write(PIN_RESET_K66, 1);
  app_main_board.init();

  // Start application timers.
  err_code = app_timer_start(sd_timer_id, YR_MICROS_INTERVAL, NULL);
  APP_ERROR_CHECK(err_code);
  err_code = app_timer_start(main_loop_timer_id, YR_MICROS_INTERVAL, NULL);
  APP_ERROR_CHECK(err_code);

  // // Initialize test system
  // init_test_system();

  // // Optionally enable automatic testing (uncomment to enable)
  // // set_test_mode(true);

  // NRF_LOG_INFO("=== BLE Central+Peripheral Application Started ===");
  // NRF_LOG_INFO("Central mode: %s", getCentralMode() ? "ENABLED" : "DISABLED");
  // NRF_LOG_INFO("Use trigger_manual_test() to run tests manually");
  // NRF_LOG_INFO("Use set_test_mode(true) to enable automatic testing");

  // Enter main loop.
  for (;;) {
    idle_state_handle();
  }
}


/**@brief Function to set paired device serial number and start scanning.
 */
void setPairedDeviceAndStartScan(const char* device_serial_number) {
    if (!is_central) {
        NRF_LOG_WARNING("Central mode is not enabled");
        return;
    }

    if (device_serial_number == NULL || strlen(device_serial_number) == 0) {
        NRF_LOG_ERROR("Invalid device serial number");
        return;
    }

    NRF_LOG_INFO("Setting paired device serial number: [%s]", device_serial_number);

    // 将配对设备的序列号写入flash
    app_main_board.WritePairedDeviceSerialNumberToFlash((char*)device_serial_number, strlen(device_serial_number));

    memset(m_target_periph_name, 0, sizeof(m_target_periph_name));
    snprintf(m_target_periph_name, sizeof(m_target_periph_name), "YRobot_%s", device_serial_number);

    NRF_LOG_INFO("Updated target device name to: [%s]", m_target_periph_name);

    nrf_ble_scan_stop();
    scan_init();
    scan_start();

    NRF_LOG_INFO("Started scanning for paired device: [%s]", m_target_periph_name);
}

/**@brief Function to clear paired device and stop scanning.
 */
void clearPairedDevice(void) {
    NRF_LOG_INFO("Clearing paired device serial number");

    char invalid_serial[32];
    memset(invalid_serial, 0xFF, sizeof(invalid_serial));
    app_main_board.WritePairedDeviceSerialNumberToFlash(invalid_serial, sizeof(invalid_serial));

    if (is_central) {
        nrf_ble_scan_stop();
        NRF_LOG_INFO("Stopped scanning - no paired device");
    }
}

// /**@brief Function to test NUS Client functionality.
//  */
// void testNUSClient(void) {
//     NRF_LOG_INFO("=== Testing NUS Client Functionality ===");

//     NRF_LOG_INFO("Central mode enabled: %s", getCentralMode() ? "YES" : "NO");
//     NRF_LOG_INFO("Central connected: %s", isCentralConnected() ? "YES" : "NO");
//     NRF_LOG_INFO("NUS service discovered: %s", m_nus_service_discovered ? "YES" : "NO");

//     if (isCentralConnected() && m_nus_service_discovered) {
//         // Test sending data through NUS Client
//         uint8_t test_data[] = "Hello from NUS Client!";
//         uint32_t err_code = sendBluetoothMsgCentral(test_data, sizeof(test_data) - 1);

//         if (err_code == NRF_SUCCESS) {
//             NRF_LOG_INFO("NUS Client test data sent successfully");
//         } else {
//             NRF_LOG_ERROR("NUS Client test failed: 0x%X", err_code);
//         }

//         // Test protocol data sending
//         uint8_t protocol_data[] = "Protocol test data";
//         err_code = sendDataToDevice(DATA_TYPE_LOCAL_PROCESS, protocol_data, sizeof(protocol_data) - 1);

//         if (err_code == NRF_SUCCESS) {
//             NRF_LOG_INFO("Protocol data sent successfully");
//         } else {
//             NRF_LOG_ERROR("Protocol data send failed: 0x%X", err_code);
//         }
//     } else {
//         NRF_LOG_WARNING("Cannot test NUS Client - not connected or service not discovered");

//         if (!isCentralConnected()) {
//             NRF_LOG_INFO("Attempting to start scanning...");
//             if (getCentralMode()) {
//                 scan_start();
//             } else {
//                 NRF_LOG_WARNING("Central mode is disabled");
//             }
//         }
//     }

//     NRF_LOG_INFO("=== NUS Client Test Complete ===");
// }

/**@brief Function to diagnose central connection status.
 */
void diagnoseCentralConnection(void) {
    NRF_LOG_INFO("=== Central Connection Diagnosis ===");

    // 基本状态
    NRF_LOG_INFO("Central mode enabled: %s", is_central ? "YES" : "NO");
    NRF_LOG_INFO("Central connection handle: 0x%04X", m_central_conn_handle);
    NRF_LOG_INFO("Central connected: %s", isCentralConnected() ? "YES" : "NO");

    // NUS服务状态
    NRF_LOG_INFO("NUS service discovered: %s", m_nus_service_discovered ? "YES" : "NO");
    NRF_LOG_INFO("NUS client connection handle: 0x%04X", m_ble_nus_c.conn_handle);

    // 句柄状态
    NRF_LOG_INFO("NUS RX handle: 0x%04X", m_ble_nus_c.handles.nus_rx_handle);
    NRF_LOG_INFO("NUS TX handle: 0x%04X", m_ble_nus_c.handles.nus_tx_handle);
    NRF_LOG_INFO("NUS TX CCCD handle: 0x%04X", m_ble_nus_c.handles.nus_tx_cccd_handle);

    // 传输准备状态
    bool ready = isCentralReadyForTransmission();
    NRF_LOG_INFO("Ready for transmission: %s", ready ? "YES" : "NO");

    if (!ready) {
        NRF_LOG_WARNING("Central connection is not ready for data transmission");
        if (m_central_conn_handle == BLE_CONN_HANDLE_INVALID) {
            NRF_LOG_WARNING("- No central connection established");
        }
        if (!m_nus_service_discovered) {
            NRF_LOG_WARNING("- NUS service not discovered yet");
        }
        if (m_ble_nus_c.conn_handle != m_central_conn_handle) {
            NRF_LOG_WARNING("- NUS client handle mismatch");
        }
        if (m_ble_nus_c.handles.nus_rx_handle == BLE_GATT_HANDLE_INVALID) {
            NRF_LOG_WARNING("- NUS RX handle invalid");
        }
    }

    NRF_LOG_INFO("=== Diagnosis Complete ===");
}

/**
 * @}
 */
