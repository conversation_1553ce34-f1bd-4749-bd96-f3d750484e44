#include "yr_util.h"
#include "app_main_board.h"
#include "ble_central_peripheral.h"
#include "nrf_delay.h"

nrf_drv_spis_config_t spis_config;

//----------------------------------------------------------------------------//
//  CENTRAL MODE VARIABLES
//----------------------------------------------------------------------------//
NRF_BLE_SCAN_DEF(m_scan);                                  /**< Scanning module instance. */
static bool is_central = false;                            /**< Flag to enable/disable central mode. */
static char m_target_periph_name[] = "YRobot";            /**< Name of the device to search for. */
static uint16_t m_central_conn_handle = BLE_CONN_HANDLE_INVALID; /**< Handle for central connection. */

//----------------------------------------------------------------------------//
//  BLE RX
//----------------------------------------------------------------------------//
void nus_data_handler(ble_nus_evt_t *p_evt) {
  if (p_evt->type == BLE_NUS_EVT_RX_DATA) {
    app_main_board.handleBleRx((uint8_t *)p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
    bool debug = false;
    if (debug) {
      NRF_LOG_INFO("Received data from BLE NUS. Writing data on UART.");
      NRF_LOG_HEXDUMP_INFO(p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
    }
  }
}

//----------------------------------------------------------------------------//
//  SPI RX
//----------------------------------------------------------------------------//
void spis_event_handler(nrf_drv_spis_event_t event) {
#ifdef USE_MOCK_TARGET
  return;
#endif  // USE_MOCK_TARGET
  if (event.evt_type == NRF_DRV_SPIS_XFER_DONE) {
    app_main_board.handleSpiRx(event.rx_amount);
  } else if (event.evt_type == NRF_DRV_SPIS_BUFFERS_SET_DONE) {
    // NRF_LOG_INFO("-- SPI BUFFERS SET [%d]", app_main_board.msg_stats[AppMainBoard::SPI_TX].getCount());
    app_main_board.msg_stats[AppMainBoard::SPI_TX].add();
  }
}

uint32_t sendBluetoothMsgProxy(uint8_t *buf, uint16_t len) {
  return g_send_bluetooth(buf, len);
}

/**@brief Function to send data via central connection.
 */
unsigned int sendBluetoothMsgCentral(unsigned char *buf, unsigned short len) {
  if (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) {
    uint16_t length = len;
    return ble_nus_data_send(&m_nus, buf, &length, m_central_conn_handle);
  }
  return NRF_ERROR_INVALID_STATE;
}

/**@brief Function to check if central connection is active.
 */
int isCentralConnected(void) {
  return (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) ? 1 : 0;
}

/**@brief Function to check if peripheral connection is active.
 */
int isPeripheralConnected(void) {
  return (m_conn_handle != BLE_CONN_HANDLE_INVALID) ? 1 : 0;
}

void idle_state_handle(void) {
  app_main_board.update();
}

void timer_main_loop_handler(void *p_context) {
}

//----------------------------------------------------------------------------//
//  CENTRAL MODE FUNCTIONS
//----------------------------------------------------------------------------//
 /**@brief Function to check and log BLE state.
 */
static void log_ble_state(void) {
  NRF_LOG_INFO("=== BLE State Check ===");
  NRF_LOG_INFO("Central handle: 0x%04X", m_central_conn_handle);
  NRF_LOG_INFO("Peripheral handle: 0x%04X", m_conn_handle);
  NRF_LOG_INFO("Central mode enabled: %s", is_central ? "YES" : "NO");
  NRF_LOG_INFO("=====================");
}

/**@brief Function for starting the scanning.
 */
static void scan_start(void) {
  ret_code_t err_code;

  // Log current state for debugging
  log_ble_state();

  // Check if we're already connected as central
  if (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) {
    NRF_LOG_WARNING("Already connected as central, not starting scan");
    return;
  }

  NRF_LOG_INFO("Starting scanning for devices named: %s", m_target_periph_name);

  err_code = nrf_ble_scan_start(&m_scan);
  if (err_code == NRF_SUCCESS) {
    NRF_LOG_INFO("Scanning started successfully");
  } else {
    NRF_LOG_ERROR("Failed to start scanning: 0x%x (%s)", err_code,
                  (err_code == NRF_ERROR_INVALID_STATE) ? "INVALID_STATE" :
                  (err_code == NRF_ERROR_BUSY) ? "BUSY" : "OTHER");
  }
}

/**@brief Function for handling Scanning Module events.
 */
static void scan_evt_handler(scan_evt_t const * p_scan_evt) {
  ret_code_t err_code;

  switch(p_scan_evt->scan_evt_id) {
    case NRF_BLE_SCAN_EVT_FILTER_MATCH: {
      ble_gap_evt_adv_report_t const * p_adv = p_scan_evt->params.filter_match.p_adv_report;
      ble_gap_scan_params_t const * p_scan_param = p_scan_evt->p_scan_params;

      // Debug: Print raw advertising data
      NRF_LOG_INFO("Raw adv data (%d bytes):", p_adv->data.len);
      for (uint8_t i = 0; i < p_adv->data.len && i < 31; i++) {
        NRF_LOG_INFO("  [%02d]: 0x%02X", i, p_adv->data.p_data[i]);
      }

      // Manual device name extraction from raw data
      char dev_name[64] = {0};
      bool name_found = false;

      // Parse advertising data manually
      uint8_t pos = 0;
      while (pos < p_adv->data.len) {
        uint8_t length = p_adv->data.p_data[pos];
        if (length == 0 || pos + length >= p_adv->data.len) break;

        uint8_t type = p_adv->data.p_data[pos + 1];

        // Check for Complete Local Name (0x09) or Shortened Local Name (0x08)
        if (type == 0x09 || type == 0x08) {
          uint8_t name_len = length - 1; // Subtract type byte
          if (name_len > 0 && name_len < sizeof(dev_name)) {
            memcpy(dev_name, &p_adv->data.p_data[pos + 2], name_len);
            dev_name[name_len] = '\0';
            name_found = true;
            NRF_LOG_INFO("Manual name extraction: %s (type: 0x%02X, len: %d)", dev_name, type, name_len);
            break;
          }
        }

        pos += length + 1;
      }

      // Fallback to SDK function if manual parsing failed
      if (!name_found) {
        name_found = ble_advdata_name_find(p_adv->data.p_data, p_adv->data.len, dev_name);
      }

      // Ensure null termination
      dev_name[sizeof(dev_name) - 1] = '\0';

      if (!name_found || strlen(dev_name) == 0) {
        strncpy(dev_name, "Unknown Device", sizeof(dev_name) - 1);
      }

      NRF_LOG_INFO("Found device: %s (name_found: %d)", dev_name, name_found);
      NRF_LOG_INFO("MAC: %02X:%02X:%02X:%02X:%02X:%02X",
          p_adv->peer_addr.addr[5], p_adv->peer_addr.addr[4], p_adv->peer_addr.addr[3],
          p_adv->peer_addr.addr[2], p_adv->peer_addr.addr[1], p_adv->peer_addr.addr[0]);
      NRF_LOG_INFO("RSSI: %d dBm, Data len: %d", p_adv->rssi, p_adv->data.len);

      // Check if we're already connected or connecting
      if (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) {
        NRF_LOG_WARNING("Already connected as central, ignoring new device");
        return;
      }

      // Check signal strength - skip if too weak
      if (p_adv->rssi < -80) {
        NRF_LOG_WARNING("Signal too weak (RSSI: %d), skipping connection", p_adv->rssi);
        return;
      }

      // Stop scanning before attempting connection - use direct SoftDevice call
      ret_code_t stop_err = sd_ble_gap_scan_stop();
      NRF_LOG_INFO("Stopped scanning (result: 0x%x), attempting connection...", stop_err);

      // Add longer delay to ensure scan is fully stopped
      nrf_delay_ms(200);

      // Use very conservative connection parameters
      ble_gap_conn_params_t conn_param = {
        .min_conn_interval = MSEC_TO_UNITS(50, UNIT_1_25_MS),   // 50ms
        .max_conn_interval = MSEC_TO_UNITS(100, UNIT_1_25_MS),  // 100ms
        .slave_latency = 0,
        .conn_sup_timeout = MSEC_TO_UNITS(6000, UNIT_10_MS)     // 6s
      };

      NRF_LOG_INFO("Connection params: min=%d, max=%d, latency=%d, timeout=%d",
                   conn_param.min_conn_interval, conn_param.max_conn_interval,
                   conn_param.slave_latency, conn_param.conn_sup_timeout);

      // First, try with simplified scan parameters
      ble_gap_scan_params_t simple_scan_params = {
        .active = 0,
        .interval = MSEC_TO_UNITS(100, UNIT_0_625_MS),
        .window = MSEC_TO_UNITS(50, UNIT_0_625_MS),
        .timeout = 0,
        .scan_phys = BLE_GAP_PHY_2MBPS,
        .filter_policy = BLE_GAP_SCAN_FP_ACCEPT_ALL,
      };

      // Try connection with custom parameters first
      NRF_LOG_INFO("Attempting connection with custom parameters...");
      err_code = sd_ble_gap_connect(&p_adv->peer_addr,
                                    &simple_scan_params,
                                    &conn_param,
                                    APP_BLE_CONN_CFG_TAG);

      if (err_code == NRF_SUCCESS) {
        NRF_LOG_INFO("Connection request sent successfully");
      } else {
        NRF_LOG_ERROR("sd_ble_gap_connect() with custom params failed: 0x%x", err_code);

        // Try with NULL parameters (use defaults)
        NRF_LOG_INFO("Retrying with NULL connection parameters...");
        nrf_delay_ms(100);

        err_code = sd_ble_gap_connect(&p_adv->peer_addr,
                                      &simple_scan_params,
                                      NULL,  // Use default connection parameters
                                      APP_BLE_CONN_CFG_TAG);

        if (err_code == NRF_SUCCESS) {
          NRF_LOG_INFO("Connection request with defaults sent successfully");
        } else {
          NRF_LOG_ERROR("sd_ble_gap_connect() with defaults also failed: 0x%x (%s)", err_code,
                        (err_code == NRF_ERROR_INVALID_STATE) ? "INVALID_STATE" :
                        (err_code == NRF_ERROR_INVALID_PARAM) ? "INVALID_PARAM" :
                        (err_code == NRF_ERROR_BUSY) ? "BUSY" :
                        (err_code == NRF_ERROR_NO_MEM) ? "NO_MEM" :
                        (err_code == NRF_ERROR_CONN_COUNT) ? "CONN_COUNT" : "OTHER");

          // Try one more time with minimal parameters
          NRF_LOG_INFO("Final attempt with minimal parameters...");
          nrf_delay_ms(100);

          ble_gap_conn_params_t minimal_param = {
            .min_conn_interval = MSEC_TO_UNITS(20, UNIT_1_25_MS),   // 20ms
            .max_conn_interval = MSEC_TO_UNITS(40, UNIT_1_25_MS),   // 40ms
            .slave_latency = 0,
            .conn_sup_timeout = MSEC_TO_UNITS(4000, UNIT_10_MS)     // 4s
          };

          err_code = sd_ble_gap_connect(&p_adv->peer_addr,
                                        &simple_scan_params,
                                        &minimal_param,
                                        APP_BLE_CONN_CFG_TAG);

          if (err_code == NRF_SUCCESS) {
            NRF_LOG_INFO("Connection request with minimal params sent successfully");
          } else {
            NRF_LOG_ERROR("All connection attempts failed. Final error: 0x%x", err_code);
            // Wait before restarting scan
            nrf_delay_ms(500);
            scan_start();
          }
        }
      }

    } break;

    case NRF_BLE_SCAN_EVT_NOT_FOUND: {
      NRF_LOG_DEBUG("No matching device found during scan.");
    } break;

    default:
      break;
  }
}

/**@brief Function for initialization the scanning and setting the filters.
 */
static void scan_init(void) {
  ret_code_t err_code;
  nrf_ble_scan_init_t init_scan;

  memset(&init_scan, 0, sizeof(init_scan));
  init_scan.connect_if_match = false;
  init_scan.conn_cfg_tag     = APP_BLE_CONN_CFG_TAG;

  err_code = nrf_ble_scan_init(&m_scan, &init_scan, scan_evt_handler);
  APP_ERROR_CHECK(err_code);

  // Set filter to scan for devices with specific name
  err_code = nrf_ble_scan_filter_set(&m_scan,
                                     SCAN_NAME_FILTER,
                                     m_target_periph_name);
  APP_ERROR_CHECK(err_code);

  // Enable name filter
  err_code = nrf_ble_scan_filters_enable(&m_scan,
                                         NRF_BLE_SCAN_NAME_FILTER,
                                         false);
  APP_ERROR_CHECK(err_code);

  NRF_LOG_INFO("Scan initialized with filters for: %s", m_target_periph_name);
}

void handleConnection(bool state) {
  if (state) {
    g_time_connected = millis();
    app_main_board.handleConnected();
  } else {
    app_main_board.handleDisconnected();
  }
}

#ifdef __cplusplus
extern "C" {
#endif

static void ble_evt_handler(ble_evt_t const *p_ble_evt, void *p_context) {
  uint32_t err_code;
  uint32_t count = 0;

  ble_gap_evt_t const *p_gap_evt = &p_ble_evt->evt.gap_evt;

  // NRF_LOG_INFO("\t\tble_evt_handler [%d]", p_ble_evt->header.evt_id);

  switch (p_ble_evt->header.evt_id) {
    case BLE_GATTS_EVT_HVN_TX_COMPLETE:
      count = p_ble_evt->evt.gatts_evt.params.hvn_tx_complete.count;
      app_main_board.handleTxComplete(count);
      // NRF_LOG_INFO("BLE TX Complete. N:%d",count)
      break;

    case BLE_GAP_EVT_CONNECTED: {
      uint16_t conn_handle = p_ble_evt->evt.gap_evt.conn_handle;
      uint8_t role = p_ble_evt->evt.gap_evt.params.connected.role;

      if (role == BLE_GAP_ROLE_PERIPH) {
        NRF_LOG_INFO("BLE Connected as Peripheral");
        m_conn_handle = conn_handle;
        err_code = nrf_ble_qwr_conn_handle_assign(&m_qwr, m_conn_handle);
        APP_ERROR_CHECK(err_code);
      } else if (role == BLE_GAP_ROLE_CENTRAL) {
        NRF_LOG_INFO("BLE Connected as Central");
        m_central_conn_handle = conn_handle;
        // Stop scanning when connected as central
        nrf_ble_scan_stop();
      }

      err_code = bsp_indication_set(BSP_INDICATE_CONNECTED);
      APP_ERROR_CHECK(err_code);
      handleConnection(true);

      // err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_CONN, conn_handle, 4);
      // APP_ERROR_CHECK(err_code);

      err_code = sd_ble_gap_phy_update(p_gap_evt->conn_handle, &m_test_params.phys);
      APP_ERROR_CHECK(err_code);
      //
      // err_code = nrf_ble_gatt_data_length_set(&m_gatt, BLE_CONN_HANDLE_INVALID, 251);
      // APP_ERROR_CHECK(err_code);
    } break;

    case BLE_GAP_EVT_DISCONNECTED: {
      uint16_t conn_handle = p_ble_evt->evt.gap_evt.conn_handle;

      NRF_LOG_INFO("BLE Disconnected");
      NRF_LOG_INFO("BLE Disconnect reason %d", p_ble_evt->evt.gap_evt.params.disconnected.reason);

      // Check which connection was disconnected
      if (conn_handle == m_conn_handle) {
        NRF_LOG_INFO("Peripheral connection disconnected");
        m_conn_handle = BLE_CONN_HANDLE_INVALID;
      } else if (conn_handle == m_central_conn_handle) {
        NRF_LOG_INFO("Central connection disconnected");
        m_central_conn_handle = BLE_CONN_HANDLE_INVALID;

        // Restart scanning if central mode is enabled
        if (is_central) {
          scan_start();
        }
      }

      // LED indication will be changed when advertising starts.
      handleConnection(false);
    } break;

    case BLE_GAP_EVT_PHY_UPDATE_REQUEST: {
      NRF_LOG_INFO("PHY update request.");
      // ble_gap_phys_t phys = {
      //   //        .rx_phys = BLE_GAP_PHY_AUTO,
      //   //        .tx_phys = BLE_GAP_PHY_AUTO,
      // };
      // phys.rx_phys = BLE_GAP_PHY_AUTO;
      // phys.tx_phys = BLE_GAP_PHY_AUTO;
      // err_code = sd_ble_gap_phy_update(p_ble_evt->evt.gap_evt.conn_handle, &phys);
      ble_gap_evt_t const *p_gap_evt = &p_ble_evt->evt.gap_evt;
      err_code = sd_ble_gap_phy_update(p_gap_evt->conn_handle, &m_test_params.phys);
      APP_ERROR_CHECK(err_code);
    } break;
    case BLE_GAP_EVT_SEC_PARAMS_REQUEST:
      NRF_LOG_INFO("BLE_GAP_EVT_SEC_PARAMS_REQUEST");
      // Pairing not supported
      err_code = sd_ble_gap_sec_params_reply(m_conn_handle, BLE_GAP_SEC_STATUS_PAIRING_NOT_SUPP, NULL, NULL);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GATTS_EVT_SYS_ATTR_MISSING:
      NRF_LOG_INFO("GATTS_EVT_SYS_ATTR_MISSING");
      // No system attributes have been stored.
      err_code = sd_ble_gatts_sys_attr_set(m_conn_handle, NULL, 0, 0);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GATTC_EVT_TIMEOUT:
      // Disconnect on GATT Client timeout event.
      NRF_LOG_INFO("GATT Client Timeout.");
      err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gattc_evt.conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
      APP_ERROR_CHECK(err_code);
      break;

    case BLE_GAP_EVT_ADV_REPORT:
      // This event is handled by the scanning module
      break;

    case BLE_GAP_EVT_TIMEOUT:
      NRF_LOG_INFO("BLE_GAP_EVT_TIMEOUT");
      // if (p_gap_evt->params.timeout.src == BLE_GAP_TIMEOUT_SRC_ADVERTISING) {
      //   NRF_LOG_INFO("Advertising timeout");
      // } else 
      if (p_gap_evt->params.timeout.src == BLE_GAP_TIMEOUT_SRC_CONN) {
        NRF_LOG_INFO("Connection timeout - restarting scan");
        // Connection timeout, restart scanning if central mode is enabled
        if (is_central) {
          scan_start();
        }
      } else if (p_gap_evt->params.timeout.src == BLE_GAP_TIMEOUT_SRC_SCAN) {
        NRF_LOG_INFO("Scan timeout - restarting scan");
        if (is_central) {
          scan_start();
        }
      }
      break;

    case BLE_GAP_EVT_CONN_PARAM_UPDATE_REQUEST: {
      // Accept connection parameter update request
      err_code = sd_ble_gap_conn_param_update(p_gap_evt->conn_handle,
                                              &p_gap_evt->params.conn_param_update_request.conn_params);
      APP_ERROR_CHECK(err_code);
      NRF_LOG_INFO("Connection parameters updated");
    } break;

    case BLE_GATTS_EVT_TIMEOUT:
      // Disconnect on GATT Server timeout event.
      NRF_LOG_INFO("GATT Server Timeout.");
      err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gatts_evt.conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
      APP_ERROR_CHECK(err_code);
      break;
    case BLE_GAP_EVT_AUTH_KEY_REQUEST:
      NRF_LOG_INFO("BLE_GAP_EVT_AUTH_KEY_REQUEST");
      break;
    case BLE_GAP_EVT_LESC_DHKEY_REQUEST:
      NRF_LOG_INFO("BLE_GAP_EVT_LESC_DHKEY_REQUEST");
      break;
    case BLE_GAP_EVT_AUTH_STATUS:
      NRF_LOG_INFO("BLE_GAP_EVT_AUTH_STATUS: status=0x%x bond=0x%x lv4: %d kdist_own:0x%x kdist_peer:0x%x",
                   p_ble_evt->evt.gap_evt.params.auth_status.auth_status,
                   p_ble_evt->evt.gap_evt.params.auth_status.bonded,
                   p_ble_evt->evt.gap_evt.params.auth_status.sm1_levels.lv4,
                   *((uint8_t *)&p_ble_evt->evt.gap_evt.params.auth_status.kdist_own),
                   *((uint8_t *)&p_ble_evt->evt.gap_evt.params.auth_status.kdist_peer));
      break;
    default:
      // No implementation needed.
      break;
  }
}


/**@brief Function for the SoftDevice initialization.
 *
 * @details This function initializes the SoftDevice and the BLE event interrupt.
 */
void ble_stack_init(void) {
  ret_code_t err_code;

  // NRF_LOG_INFO("ble_stack_init");

  err_code = nrf_sdh_enable_request();
  APP_ERROR_CHECK(err_code);

  // Configure the BLE stack using the default settings.
  // Fetch the start address of the application RAM.
  uint32_t ram_start = 0;
  err_code = nrf_sdh_ble_default_cfg_set(APP_BLE_CONN_CFG_TAG, &ram_start);
  APP_ERROR_CHECK(err_code);

  ble_cfg_t ble_cfg;
  memset(&ble_cfg, 0, sizeof(ble_cfg));
  ble_cfg.conn_cfg.conn_cfg_tag = APP_BLE_CONN_CFG_TAG;
  // ble_cfg.conn_cfg.params.gattc_conn_cfg.write_cmd_tx_queue_size = 10;
  // ble_cfg.conn_cfg.params.gattc_conn_cfg.write_cmd_tx_queue_size = 8;
  // ble_cfg.conn_cfg.params.gatts_conn_cfg.hvn_tx_queue_size = 10;
  // ble_cfg.conn_cfg.params.gatts_conn_cfg.hvn_tx_queue_size = 7;
  err_code = sd_ble_cfg_set(BLE_CONN_CFG_GATTC, &ble_cfg, ram_start);
  APP_ERROR_CHECK(err_code);

  // Enable BLE stack.
  err_code = nrf_sdh_ble_enable(&ram_start);
  APP_ERROR_CHECK(err_code);

  // ble_opt_t gap_opt;
  // gap_opt.gap_opt.ext_len.rxtx_max_pdu_payload_size = 251;  // Example: set max length to 54bytes
  // err_code = sd_ble_opt_set(BLE_GAP_OPT_EXT_LEN, &gap_opt);
  // APP_ERROR_CHECK(err_code);

  // Register a handler for BLE events.
  NRF_SDH_BLE_OBSERVER(m_ble_observer, APP_BLE_OBSERVER_PRIO, ble_evt_handler, NULL);
}

#ifdef __cplusplus
}
#endif

bool initSpi(void) {
  spis_config.csn_pin = SPIS_CSN_PIN;
  spis_config.sck_pin = SPIS_SCK_PIN;
  spis_config.mosi_pin = SPIS_MOSI_PIN;
  spis_config.miso_pin = SPIS_MISO_PIN;
  spis_config.mode = NRF_SPIS_MODE_0;
  spis_config.bit_order = NRF_SPIS_BIT_ORDER_MSB_FIRST;
  spis_config.csn_pullup = NRFX_SPIS_DEFAULT_CSN_PULLUP;
  spis_config.miso_drive = NRFX_SPIS_DEFAULT_MISO_DRIVE;
  spis_config.def = NRFX_SPIS_DEFAULT_DEF;
  spis_config.orc = NRFX_SPIS_DEFAULT_ORC;
  spis_config.irq_priority = NRFX_SPIS_DEFAULT_CONFIG_IRQ_PRIORITY;

  APP_ERROR_CHECK(nrf_drv_spis_init(&spis, &spis_config, spis_event_handler));
  NRF_LOG_INFO("Init SPIS");
  return true;
}

bool initBle(void) {
  bool erase_bonds = false;
  ret_code_t err_code;

  ble_stack_init();
  gap_params_init();
  gatt_init();
  services_init();
  advertising_init();
  conn_params_init();

  gatt_mtu_set(m_test_params.att_mtu);
  conn_evt_len_ext_set(m_test_params.conn_evt_len_ext_enabled);

  print_ble_config();

  // sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_CONN, BLE_CONN_HANDLE_INVALID, transmitPower);
  err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_ADV, m_advertising.adv_handle, 4);
  APP_ERROR_CHECK(err_code);
  // err_code = sd_ble_gap_tx_power_set(BLE_GAP_TX_POWER_ROLE_ADV, BLE_CONN_HANDLE_INVALID, 1);

  // Start execution.
  NRF_LOG_INFO("\r\nUART started.");
  NRF_LOG_INFO("Debug logging for UART over RTT started.");

  // Initialize and start central mode if enabled
  if (is_central) {
    NRF_LOG_INFO("Initializing Central mode");
    scan_init();
    scan_start();
  } else {
    NRF_LOG_INFO("Central mode disabled");
  }

  // Always start advertising (peripheral mode)
  NRF_LOG_INFO("Starting Peripheral mode (advertising)");
  advertising_start(erase_bonds);

  return true;
}

/**@brief Function to enable/disable central mode.
 */
void setCentralMode(int enable) {
  is_central = (enable != 0);
  NRF_LOG_INFO("Central mode %s", is_central ? "ENABLED" : "DISABLED");
}

/**@brief Function to get current central mode status.
 */
int getCentralMode(void) {
  return is_central ? 1 : 0;
}

/**@brief Test function to demonstrate central/peripheral functionality.
 */
void testCentralPeripheralMode(void) {
  static uint32_t test_counter = 0;
  test_counter++;

  NRF_LOG_INFO("=== Central/Peripheral Test #%d ===", test_counter);
  NRF_LOG_INFO("Central mode: %s", getCentralMode() ? "ENABLED" : "DISABLED");
  NRF_LOG_INFO("Central connected: %s", isCentralConnected() ? "YES" : "NO");
  NRF_LOG_INFO("Peripheral connected: %s", isPeripheralConnected() ? "YES" : "NO");

  // Test sending data if connections are active
  if (isCentralConnected()) {
    uint8_t test_data[] = "Test from Central";
    uint32_t result = sendBluetoothMsgCentral(test_data, sizeof(test_data) - 1);
    NRF_LOG_INFO("Central TX result: 0x%X", result);
  }

  if (isPeripheralConnected()) {
    uint8_t test_data[] = "Test from Peripheral";
    uint32_t result = sendBluetoothMsgProxy(test_data, sizeof(test_data) - 1);
    NRF_LOG_INFO("Peripheral TX result: 0x%X", result);
  }
}

/**@brief Application main function.
 */
int main(void) {
  bool erase_bonds;
  ret_code_t err_code;

  log_init();
  //  NRF_LOG_DEFAULT_BACKENDS_INIT();

#ifdef USE_NRF_DFU
  // Initialize the async SVCI interface to bootloader before any interrupts are enabled.
  err_code = ble_dfu_buttonless_async_svci_init();
  APP_ERROR_CHECK(err_code);
#endif  // USE_NRF_DFU

  timers_init();
  buttons_leds_init(&erase_bonds);

#ifdef USE_UART
  uart_init();
#endif  // USE_UART

  power_management_init();

#ifdef USE_NRF_DFU
  peer_manager_init();
#endif  // USE_NRF_DFU
        // peer_manager_init();

#ifdef USE_FDS
  app_main_board.InitFds();
#endif  // USE_FDS

  // Set central mode - change this to 1 to enable central functionality
  setCentralMode(true);  // Set to 1 to enable both central and peripheral modes

  initBle();
  initSpi();

  nrf_gpio_cfg_output(PIN_SPI_WRITE_INT);
  // app_main_board.enableBleIntPin(true);
  nrf_gpio_cfg_output(PIN_RESET_K66);
  nrf_gpio_pin_write(PIN_RESET_K66, 1);
  app_main_board.init();

  // Start application timers.
  err_code = app_timer_start(sd_timer_id, YR_MICROS_INTERVAL, NULL);
  APP_ERROR_CHECK(err_code);
  err_code = app_timer_start(main_loop_timer_id, YR_MICROS_INTERVAL, NULL);
  APP_ERROR_CHECK(err_code);

  // Enter main loop.
  for (;;) {
    idle_state_handle();
  }
}


/**
 * @}
 */
