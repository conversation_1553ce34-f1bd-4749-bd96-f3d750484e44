/**
 * @file yrobot_discovery_example.cpp
 * @brief YRobot设备发现功能使用示例
 * 
 * 这个文件展示了如何使用YRobot设备发现功能来扫描和显示
 * 所有包含"YRobot"字符串的BLE设备信息。
 */

#include "ble_central_peripheral.h"
#include "yr_util.h"

// 定时器ID（需要在实际项目中定义）
// APP_TIMER_DEF(discovery_timer_id);

/**
 * @brief 基本的YRobot设备发现示例
 */
void basic_discovery_example(void) {
    NRF_LOG_INFO("=== Basic YRobot Discovery Example ===");
    
    // 1. 启用central模式
    setCentralMode(1);
    
    // 2. 验证模式是否启用
    if (getCentralMode()) {
        NRF_LOG_INFO("Central mode enabled successfully");
        
        // 3. 开始发现YRobot设备
        startYRobotDiscovery();
        
        NRF_LOG_INFO("Discovery started. Devices will be shown as they are found.");
        NRF_LOG_INFO("Call stopYRobotDiscovery() to stop and see summary.");
    } else {
        NRF_LOG_ERROR("Failed to enable central mode");
    }
}

/**
 * @brief 带状态检查的发现示例
 */
void discovery_with_status_check(void) {
    NRF_LOG_INFO("=== Discovery with Status Check Example ===");
    
    // 启用central模式
    setCentralMode(1);
    
    if (getCentralMode()) {
        // 清除之前的发现记录并开始新的扫描
        startYRobotDiscovery();
        
        // 模拟等待一段时间后检查状态
        // 在实际应用中，这可以通过定时器或用户交互触发
        
        // 检查当前发现的设备数量
        int count = getDiscoveredDevicesCount();
        NRF_LOG_INFO("Currently discovered %d YRobot devices", count);
        
        if (count > 0) {
            NRF_LOG_INFO("Found some devices! Continue scanning or call stopYRobotDiscovery() to see details.");
        } else {
            NRF_LOG_INFO("No YRobot devices found yet. Make sure devices are advertising nearby.");
        }
    }
}

/**
 * @brief 定时发现示例（需要定时器支持）
 */
void timed_discovery_example(void) {
    NRF_LOG_INFO("=== Timed Discovery Example ===");
    
    setCentralMode(1);
    
    if (getCentralMode()) {
        NRF_LOG_INFO("Starting 30-second YRobot device discovery...");
        startYRobotDiscovery();
        
        // 在实际应用中，设置30秒定时器
        // app_timer_start(discovery_timer_id, APP_TIMER_TICKS(30000), NULL);
        
        NRF_LOG_INFO("Timer would stop discovery after 30 seconds");
    }
}

/**
 * @brief 定时器回调函数示例
 */
void discovery_timer_callback(void *p_context) {
    NRF_LOG_INFO("Discovery timer expired - stopping discovery");
    stopYRobotDiscovery();
    
    int total_found = getDiscoveredDevicesCount();
    NRF_LOG_INFO("Discovery completed. Total YRobot devices found: %d", total_found);
}

/**
 * @brief 交互式发现示例
 */
void interactive_discovery_example(void) {
    NRF_LOG_INFO("=== Interactive Discovery Example ===");
    
    static bool discovery_active = false;
    
    if (!discovery_active) {
        // 开始发现
        setCentralMode(1);
        if (getCentralMode()) {
            startYRobotDiscovery();
            discovery_active = true;
            NRF_LOG_INFO("Discovery started. Call this function again to stop.");
        }
    } else {
        // 停止发现
        stopYRobotDiscovery();
        discovery_active = false;
        NRF_LOG_INFO("Discovery stopped. Call this function again to restart.");
    }
}

/**
 * @brief 条件发现示例
 */
void conditional_discovery_example(void) {
    NRF_LOG_INFO("=== Conditional Discovery Example ===");
    
    // 只在没有peripheral连接时进行发现
    if (!isPeripheralConnected()) {
        NRF_LOG_INFO("No peripheral connection - starting YRobot discovery");
        setCentralMode(1);
        startYRobotDiscovery();
    } else {
        NRF_LOG_INFO("Peripheral connected - skipping discovery to avoid interference");
    }
}

/**
 * @brief 周期性状态报告示例
 */
void periodic_status_report(void) {
    static uint32_t report_counter = 0;
    report_counter++;
    
    NRF_LOG_INFO("=== Status Report #%d ===", report_counter);
    NRF_LOG_INFO("Central mode: %s", getCentralMode() ? "ENABLED" : "DISABLED");
    NRF_LOG_INFO("Peripheral connected: %s", isPeripheralConnected() ? "YES" : "NO");
    NRF_LOG_INFO("Central connected: %s", isCentralConnected() ? "YES" : "NO");
    NRF_LOG_INFO("Discovered YRobot devices: %d", getDiscoveredDevicesCount());
    
    // 如果发现了设备但还在扫描，可以选择停止并查看详情
    int discovered = getDiscoveredDevicesCount();
    if (discovered > 0 && discovered % 5 == 0) {  // 每发现5个设备显示一次汇总
        NRF_LOG_INFO("Reached %d devices - showing summary:", discovered);
        stopYRobotDiscovery();
        // 可以选择重新开始扫描
        startYRobotDiscovery();
    }
}

/**
 * @brief 错误处理示例
 */
void error_handling_example(void) {
    NRF_LOG_INFO("=== Error Handling Example ===");
    
    // 检查前置条件
    if (!getCentralMode()) {
        NRF_LOG_WARNING("Central mode not enabled - enabling now");
        setCentralMode(1);
        
        if (!getCentralMode()) {
            NRF_LOG_ERROR("Failed to enable central mode - check configuration");
            return;
        }
    }
    
    // 检查是否已经在扫描
    // 注意：实际应用中可能需要添加扫描状态跟踪
    
    // 开始发现
    startYRobotDiscovery();
    NRF_LOG_INFO("Discovery started with error checking");
}

/**
 * @brief 集成到主循环的示例
 */
void main_loop_integration_example(void) {
    static uint32_t loop_counter = 0;
    static bool discovery_started = false;
    
    loop_counter++;
    
    // 每1000次循环检查一次（具体频率根据实际需求调整）
    if (loop_counter % 1000 == 0) {
        if (!discovery_started) {
            // 启动发现
            setCentralMode(1);
            if (getCentralMode()) {
                startYRobotDiscovery();
                discovery_started = true;
                NRF_LOG_INFO("Auto-started YRobot discovery in main loop");
            }
        } else {
            // 检查发现状态
            int count = getDiscoveredDevicesCount();
            if (count > 0) {
                NRF_LOG_INFO("Main loop check: %d YRobot devices discovered", count);
            }
        }
    }
    
    // 每10000次循环显示一次汇总（约每分钟，取决于循环频率）
    if (loop_counter % 10000 == 0 && discovery_started) {
        NRF_LOG_INFO("Periodic summary from main loop:");
        stopYRobotDiscovery();
        
        // 重新开始发现
        startYRobotDiscovery();
    }
}

/**
 * @brief 完整的应用示例
 */
void complete_application_example(void) {
    NRF_LOG_INFO("=== Complete Application Example ===");
    
    // 1. 系统初始化
    setCentralMode(1);
    
    // 2. 验证初始化
    if (getCentralMode()) {
        NRF_LOG_INFO("System initialized successfully");
        
        // 3. 开始设备发现
        startYRobotDiscovery();
        
        // 4. 在实际应用中，这里会有主循环或事件处理
        // 用户可以通过按键、UART命令或BLE命令来控制发现过程
        
        NRF_LOG_INFO("Application running. Use the following commands:");
        NRF_LOG_INFO("- Call stopYRobotDiscovery() to stop and see results");
        NRF_LOG_INFO("- Call startYRobotDiscovery() to restart discovery");
        NRF_LOG_INFO("- Call getDiscoveredDevicesCount() to check count");
        
    } else {
        NRF_LOG_ERROR("System initialization failed");
    }
}
