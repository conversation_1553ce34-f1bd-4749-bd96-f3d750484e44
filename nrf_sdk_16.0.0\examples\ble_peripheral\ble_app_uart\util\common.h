#ifndef YR_UTIL_COMMON_H_
#define YR_UTIL_COMMON_H_

#include <stdint.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

#include "nordic_common.h"
#include "nrf.h"
#include "nrf_log.h"
#include "nrf_log_ctrl.h"
#include "nrf_log_default_backends.h"

#ifdef __cplusplus
}
#endif

#include "defs.h"
#include "defs_dfu.h"
#include "crc.h"
#include "msg_stat.h"
#include "proto/comm_api.pb.h"

#include "proto_util.h"

//#define ARM_MATH_CM4
//#include "math.h"
//#include <stdint.h>
//#include <stddef.h>
//#include <stdbool.h>

#define max(a, b) (((a) > (b)) ? (a) : (b))
#define min(a, b) (((a) < (b)) ? (a) : (b))

//-----------------------------------------------------------------------------//
//  TIMER
//-----------------------------------------------------------------------------//
extern volatile uint32_t g_millisecond_counter;
uint32_t millis();
void timer_sd_handler(void *p_context);

//-----------------------------------------------------------------------------//
//  UTIL
//-----------------------------------------------------------------------------//
float bytesToFloat(uint8_t *bytes);
void floatToBytes(float val, uint8_t *bytes);
bool DoesBufferContainDigit(char *buffer, const int length);

bool checkFor2MatchingBytesBuffer(const uint8_t &b0_in, const uint8_t &b1_in, uint8_t *buffer, const size_t &len);

static bool areBothBuffersSame(uint8_t *buf1, uint8_t *buf2, uint32_t len) {
  for (uint32_t i = 0; i < len; i++) {
    if (buf1[i] != buf2[i]) {
      return false;
    }
  }
  return true;
}

static void getHex(uint8_t *buf, uint16_t len) {
  for (int i = 0; i < len; i++) {
    buf[i] = i;
  }
}

// char g_log_buf[300];

void print_buffer(uint8_t *buffer, uint16_t length, const char *str);
void print_buffer_len(uint8_t *buffer, uint16_t length, const char *str);
void print_buffer_mem(uint8_t *buffer, uint16_t length, const char *str);

void printByteArrayWidth(uint8_t *buf, const size_t &len, const size_t &width = 10);

//-----------------------------------------------------------------------------//
//  DFU
//-----------------------------------------------------------------------------//
typedef enum
{
  IDLE = 0,
  REQUESTED,
  RECEIVED
} request_state_t;

class MsgRequestType {
public:
  MsgRequestType()
    : state(IDLE)
    , time_last_requested(0) {
  }
  void setRequest() {
    time_last_requested = millis();
    state = REQUESTED;
  }
  request_state_t getState() const {
    return state;
  }
  void reset() {
    state = IDLE;
  }
  uint32_t getDt() {
    return millis() - time_last_requested;
  }
  uint32_t time_last_requested;
  request_state_t state;
};

typedef enum
{
  NOT_STARTED = 0,
  RECEIVED_MAGIC_PACKET,
  RECEIVED_MAGIC_PACKET_ACK,
  DFU_STARTED,
  SEND_PROGRAM,
  SEND_PROGRAM_SELECT,
  SEND_CRC_SIZE,
  SEND_CRC_VALUE,
  DFU_PROGRAM_DONE,
  DFU_RESET,
  DFU_DONE,
  NUM_DFU_STATES
} dfu_state_t;

typedef enum
{
  DFU_SUB_STATE_IDLE = 0,
  CLEAR_FLASH_CMD,
  WRITE_MEM_CMD,
  SEND_DATA,
  SEND_DATA_WAIT_FOR_ACK,
  SEND_DATA_DONE,
  NUM_DFU_SUB_STATES
} dfu_sub_state_t;

typedef struct dfu_write_data_t_ {
  uint32_t address;
  uint32_t size;
  dfu_write_data_t_() {
  }
  dfu_write_data_t_(uint32_t address_in, uint32_t size_in)
    : address(address_in)
    , size(size_in) {
  }
} dfu_write_data_t;

// #define DFU_RING_BUF_SIZE (50)
#define DFU_RING_BUF_SIZE (20)
#define MEM_RING_BUF_SIZE (10)

#define BLE_TX_RING_BUF_SIZE (10)
#define BLE_RX_RING_BUF_SIZE (10)
#define SPI_TX_RING_BUF_SIZE (10)
#define SPI_RX_RING_BUF_SIZE (5)

typedef struct ble_packet_t_ {
  uint16_t length;
  uint8_t data[BLE_TX_BUF_SIZE];
} ble_packet_t;

typedef struct spi_packet_t_ {
  uint16_t length;
  uint8_t data[SPI_BUF_SIZE];
} spi_packet_t;

//-----------------------------------------------------------------------------//
//  STATES
//-----------------------------------------------------------------------------//
typedef enum
{
  STATE_RECEIVED = 0,
  STATE_PROCESSED,
  NUM_BUFFER_STATES
} buffer_state_t;

typedef enum
{
  SPI_LISTENING = 0,
  SPI_RECEIVED,
  SPI_PROCESSED,
  SPI_SET,
  SPI_SENT
} spi_state_t;

typedef enum
{
  SPI_TX_SET = 0,
  SPI_TX_SENT
} spi_tx_state_t;

const char *dfu_state_str(dfu_state_t state);
const char *dfu_sub_state_str(dfu_sub_state_t state);
const char *spi_state_str(spi_state_t state);

//-----------------------------------------------------------------------------//
//  TYPES
//-----------------------------------------------------------------------------//
template <typename T>
struct buffer_handle_t {
  T *buffer;
  size_t len;
  buffer_handle_t(T *buffer_in, const size_t &len_in)
    : buffer(buffer_in)
    , len(len_in) {
  }
};

typedef buffer_handle_t<char> char_buffer_handle_t;
typedef buffer_handle_t<uint8_t> uint8_buffer_handle_t;

class state_change_t {
public:
  state_change_t(const bool &v = false)
    : state(v)
    , state_last(v)
    , time_(0)
    , time_last_(0) {
  }

  void setVal(const bool &v) {
    state = v;
    time_ = millis();
  }

  bool getVal() const {
    return state;
  }

  bool getValLast() const {
    return state_last;
  }

  uint32_t getTime() {
    return time_;
  }

  void update() {
    state_last = state;
    time_last_ = time_;
  }

  bool changed() {
    return state != state_last;
  }

  uint32_t getTimeDiff() const {
    return time_ - time_last_;
  }

  bool state;
  bool state_last;
  uint32_t time_;
  uint32_t time_last_;
};


template <class T>
class val_change_t {
public:
  val_change_t(const T &val = 0)
    : val_(val)
    , val_last_(val)
    , time_(0)
    , time_last_(0) {
  }

  T getVal() const {
    return val_;
  }
  T getValLast() const {
    return val_last_;
  }
  void setVal(const T &v) {
    val_ = v;
    time_ = millis();
  }
  uint32_t getTime() {
    return time_;
  }
  void update() {
    val_last_ = val_;
    time_last_ = time_;
  }
  bool changed() {
    return val_ != val_last_;
  }
  bool isConsecutive(const bool &increase = true) {
    return (val_ == (val_last_ + (increase ? 1 : -1)));
  }
  uint32_t getTimeDiff() const {
    return time_ - time_last_;
  }

private:
  T val_;
  T val_last_;
  uint32_t time_;
  uint32_t time_last_;
};

#endif  // YR_UTIL_COMMON_H_
