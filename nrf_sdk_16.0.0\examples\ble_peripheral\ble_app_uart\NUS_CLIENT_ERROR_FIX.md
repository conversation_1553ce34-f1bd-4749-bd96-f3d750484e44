# NUS Client 错误修复说明

## 问题描述

在Central模式连接到Peripheral设备并发现NUS服务后，尝试启用TX特征值通知时出现以下错误：

```
<info> app: NUS service discovered on the server.
<error> app: ERROR 8 [NRF_ERROR_INVALID_STATE] at main.cpp:778
```

错误代码8对应 `NRF_ERROR_INVALID_STATE`，表示在调用 `ble_nus_c_tx_notif_enable()` 时状态无效。

## 根本原因分析

通过分析Nordic SDK源码，发现 `ble_nus_c_tx_notif_enable()` 函数会检查两个关键条件：

1. **连接句柄有效性**：`p_ble_nus_c->conn_handle != BLE_CONN_HANDLE_INVALID`
2. **CCCD句柄有效性**：`p_ble_nus_c->handles.nus_tx_cccd_handle != BLE_GATT_HANDLE_INVALID`

如果任一条件不满足，函数会返回 `NRF_ERROR_INVALID_STATE`。

## 实施的修复方案

### 1. 增强错误处理和调试信息

#### 修改前：
```cpp
// Enable TX notifications
err_code = ble_nus_c_tx_notif_enable(p_ble_nus_c);
APP_ERROR_CHECK(err_code);
```

#### 修改后：
```cpp
// Log discovered handles
NRF_LOG_INFO("NUS RX handle: 0x%04X", p_ble_nus_evt->handles.nus_rx_handle);
NRF_LOG_INFO("NUS TX handle: 0x%04X", p_ble_nus_evt->handles.nus_tx_handle);
NRF_LOG_INFO("NUS TX CCCD handle: 0x%04X", p_ble_nus_evt->handles.nus_tx_cccd_handle);

// Verify connection handle and CCCD handle before enabling notifications
NRF_LOG_INFO("Connection handle: 0x%04X", p_ble_nus_c->conn_handle);
NRF_LOG_INFO("Stored TX CCCD handle: 0x%04X", p_ble_nus_c->handles.nus_tx_cccd_handle);

if (p_ble_nus_c->handles.nus_tx_cccd_handle == BLE_GATT_HANDLE_INVALID)
{
    NRF_LOG_ERROR("TX CCCD handle is invalid, cannot enable notifications");
    m_nus_service_discovered = true; // Still mark as discovered for data sending
    break;
}

// Enable TX notifications
NRF_LOG_INFO("Enabling TX notifications...");
err_code = ble_nus_c_tx_notif_enable(p_ble_nus_c);
if (err_code != NRF_SUCCESS)
{
    NRF_LOG_ERROR("Failed to enable TX notifications: 0x%X", err_code);
    // Don't break here, still mark service as discovered
}
else
{
    NRF_LOG_INFO("TX notifications enabled successfully");
}
```

### 2. 添加NUS客户端错误处理函数

```cpp
/**@brief Function for handling NUS Client errors.
 */
static void nus_c_error_handler(uint32_t nrf_error)
{
    NRF_LOG_ERROR("NUS Client error: 0x%X", nrf_error);
}

// 在初始化时设置错误处理函数
init.error_handler = nus_c_error_handler;
```

### 3. 添加GATT队列事件处理

#### 问题：
GATT队列没有正确处理BLE事件，导致GATT操作状态不正确。

#### 解决方案：
```cpp
// 添加头文件
#include "nrf_ble_gq.h"

// 在BLE事件处理中添加GATT队列事件转发
nrf_ble_gq_on_ble_evt(p_ble_evt, &m_ble_gatt_queue);
```

### 4. 项目配置更新

#### 添加的源文件：
- `nrf_ble_gq.c` - BLE GATT队列实现

#### 添加的包含路径：
- `../../../../../../components/ble/nrf_ble_gq`

## 修复效果

### 修复前的日志：
```
<info> app: NUS service discovered on the server.
<error> app: ERROR 8 [NRF_ERROR_INVALID_STATE] at main.cpp:778
<error> app: End of error report
```

### 修复后的预期日志：
```
<info> app: NUS service discovered on the server.
<info> app: NUS RX handle: 0x000E
<info> app: NUS TX handle: 0x0010
<info> app: NUS TX CCCD handle: 0x0011
<info> app: Connection handle: 0x0000
<info> app: Stored TX CCCD handle: 0x0011
<info> app: Enabling TX notifications...
<info> app: TX notifications enabled successfully
<info> app: Connected to device with Nordic UART Service.
```

## 容错机制

即使通知启用失败，系统仍然会：

1. **标记服务为已发现**：`m_nus_service_discovered = true`
2. **允许数据发送**：可以通过RX特征值发送数据到Peripheral
3. **继续正常运行**：不会因为通知问题而中断整个连接流程

## 测试验证

### 1. 编译测试
确保所有新增的头文件和源文件正确包含，无编译错误。

### 2. 连接测试
```cpp
// 使用测试函数验证功能
testNUSClient();
```

### 3. 数据发送测试
```cpp
uint8_t test_data[] = "Hello from Central!";
uint32_t result = sendBluetoothMsgCentral(test_data, sizeof(test_data) - 1);
if (result == NRF_SUCCESS) {
    NRF_LOG_INFO("Data sent successfully");
}
```

### 4. 日志监控
观察详细的调试日志，确认：
- 所有句柄都正确获取
- 连接状态正常
- GATT操作成功

## 预防措施

### 1. 句柄验证
在所有GATT操作前验证句柄有效性。

### 2. 错误处理
为所有BLE客户端操作添加适当的错误处理。

### 3. 状态检查
在执行GATT操作前检查连接和服务状态。

### 4. 日志记录
添加详细的调试日志以便问题诊断。

## 相关文档

- `NUS_CLIENT_IMPLEMENTATION.md` - 完整实现说明
- `NUS_CLIENT_TEST_GUIDE.md` - 测试指南和故障排除
- Nordic SDK文档：`ble_nus_c.h` - NUS客户端API参考

## 注意事项

1. **MTU交换错误**：日志中的MTU交换错误是正常的，不影响NUS功能
2. **通知可选**：即使通知启用失败，仍可以发送数据到Peripheral
3. **重连机制**：如果连接断开，系统会自动重新扫描和连接
4. **内存管理**：确保GATT队列有足够的内存空间处理并发操作
