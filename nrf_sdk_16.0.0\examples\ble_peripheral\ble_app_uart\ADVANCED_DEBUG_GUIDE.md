# 高级BLE连接调试指南

## 当前问题分析

从您的日志可以看出几个关键问题：

### 1. 设备名称显示异常
```
Found device: ü
```
这表明设备名称解析有问题，可能是：
- 编码问题
- 广播数据格式问题
- 缓冲区溢出

### 2. 连接仍然失败
```
sd_ble_gap_connect() failed: 0x12 (OTHER)
```
错误代码0x12 (NRF_ERROR_INVALID_STATE) 持续出现

## 新增的调试功能

我已经在您的代码中添加了以下调试功能：

### 1. 详细的设备信息日志
```cpp
NRF_LOG_INFO("Found device: %s (name_found: %d)", dev_name, name_found);
NRF_LOG_INFO("RSSI: %d dBm, Data len: %d", p_adv->rssi, p_adv->data.len);
```

### 2. BLE状态检查
```cpp
log_ble_state();  // 显示当前连接句柄和状态
```

### 3. 双重连接尝试
- 首先尝试自定义连接参数
- 如果失败，尝试默认参数 (NULL)

### 4. 更保守的连接参数
```cpp
.min_conn_interval = MSEC_TO_UNITS(100, UNIT_1_25_MS),  // 100ms
.max_conn_interval = MSEC_TO_UNITS(200, UNIT_1_25_MS),  // 200ms
```

## 期望的新日志输出

重新测试后，您应该看到类似这样的日志：

```
=== BLE State Check ===
Central handle: 0xFFFF
Peripheral handle: 0xFFFF
Central mode enabled: YES
=====================
Starting scanning for devices named: YRobot
Scanning started successfully
Found device: [设备名称] (name_found: 1)
MAC: CB:A8:54:52:CC:6C
RSSI: -XX dBm, Data len: XX
Stopped scanning (result: 0x00), attempting connection...
Connection params: min=80, max=160, latency=0, timeout=400
Connection request sent successfully
```

## 进一步诊断步骤

### 1. 检查设备名称问题

如果设备名称仍显示异常，问题可能是：

**原因A：目标设备广播名称确实有问题**
- 检查目标设备的广播配置
- 确认设备名称编码格式

**原因B：广播数据解析问题**
- 广播数据可能不包含完整名称
- 可能只有短名称或没有名称

### 2. 分析连接失败的根本原因

如果仍然看到连接失败，请注意：

**检查点1：扫描停止确认**
```
Stopped scanning (result: 0x00)
```
如果result不是0x00，说明扫描停止失败

**检查点2：连接参数日志**
```
Connection params: min=80, max=160, latency=0, timeout=400
```
确认参数值是否合理

**检查点3：双重尝试结果**
- 第一次尝试（自定义参数）的结果
- 第二次尝试（默认参数）的结果

## 可能的解决方案

### 方案1：如果是SoftDevice配置问题

检查`sdk_config.h`中的配置：

```c
// 确保这些值正确设置
#define NRF_SDH_BLE_CENTRAL_LINK_COUNT 1
#define NRF_SDH_BLE_PERIPHERAL_LINK_COUNT 1
#define NRF_SDH_BLE_TOTAL_LINK_COUNT 2
#define NRF_SDH_BLE_GAP_EVENT_LENGTH 6
#define NRF_SDH_BLE_GATT_MAX_MTU_SIZE 247
```

### 方案2：如果是内存/资源问题

在main函数开始时添加：

```cpp
// 重置BLE状态
sd_ble_gap_adv_stop();
sd_ble_gap_scan_stop();
nrf_delay_ms(100);
```

### 方案3：如果是时序问题

增加延迟时间：

```cpp
// 在扫描停止后增加更长延迟
nrf_delay_ms(200);  // 从50ms增加到200ms
```

## 高级调试技巧

### 1. 使用nRF Connect进行对比测试

1. 在手机上安装nRF Connect
2. 扫描同一个目标设备
3. 对比设备信息和连接能力
4. 检查目标设备是否真的可连接

### 2. 创建最简单的测试设备

使用另一个nRF52设备运行标准的`ble_app_uart`作为peripheral：

```cpp
// 在peripheral设备上设置简单的广播名称
#define DEVICE_NAME "YRobotTest"
```

### 3. 监控RF活动

如果有nRF Sniffer：
1. 监控广播包内容
2. 检查连接请求是否发送
3. 查看目标设备的响应

## 临时解决方案

如果连接问题持续，可以先实现"发现但不连接"的功能：

```cpp
// 临时修改：只发现不连接
case NRF_BLE_SCAN_EVT_FILTER_MATCH: {
  // ... 设备信息日志 ...
  
  NRF_LOG_INFO("Device discovered successfully - skipping connection for now");
  
  // 不尝试连接，继续扫描
  // scan_start();  // 如果需要继续扫描
} break;
```

## 下一步行动

1. **重新编译并测试**，观察新的详细日志
2. **记录完整的日志输出**，特别是：
   - BLE状态检查结果
   - 设备名称和数据长度
   - 扫描停止结果
   - 两次连接尝试的结果

3. **如果问题仍然存在**，提供新的完整日志，我们可以：
   - 分析具体的失败点
   - 尝试更激进的解决方案
   - 考虑硬件或环境因素

## 成功连接的完整日志示例

```
=== BLE State Check ===
Central handle: 0xFFFF
Peripheral handle: 0xFFFF
Central mode enabled: YES
=====================
Starting scanning for devices named: YRobot
Scanning started successfully
Found device: YRobotDevice (name_found: 1)
MAC: CB:A8:54:52:CC:6C
RSSI: -45 dBm, Data len: 31
Stopped scanning (result: 0x00), attempting connection...
Connection params: min=80, max=160, latency=0, timeout=400
Connection request sent successfully
BLE Connected as Central
```

这个完整的日志序列表明连接过程的每一步都成功了。
