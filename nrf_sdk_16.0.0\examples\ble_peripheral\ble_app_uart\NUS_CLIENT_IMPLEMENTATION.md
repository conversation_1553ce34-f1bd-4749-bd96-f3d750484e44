# Nordic UART Service (NUS) Client Implementation

## 概述

本文档描述了为BLE Central模式添加的完整Nordic UART Service (NUS)客户端功能实现。现在Central模式可以：

1. 扫描并连接到具有"YRobot"名称的Peripheral设备
2. 自动发现NUS服务 (UUID: 6e400001-b5a3-f393-e0a9-e50e24dcca9e)
3. 获取RX和TX特征值句柄
4. 启用TX特征值通知
5. 发送和接收数据

## 实现的功能

### 1. GATT服务发现
- 连接建立后自动启动服务发现
- 发现NUS服务及其特征值
- 获取RX (6e400002) 和 TX (6e400003) 特征值句柄
- 获取CCCD句柄用于启用通知

### 2. 通知启用
- 自动启用TX特征值的通知
- 接收来自Peripheral设备的数据

### 3. 数据发送
- 通过NUS客户端API发送数据到Peripheral设备
- 支持协议数据包格式
- 集成到现有的数据路由系统

### 4. 数据接收
- 接收来自Peripheral设备的通知数据
- 通过数据路由系统处理接收的数据
- 支持转发到手机或本地处理

## 新增的文件和函数

### 主要新增函数

#### 在 main.cpp 中：
```cpp
// NUS客户端事件处理
static void ble_nus_c_evt_handler(ble_nus_c_t * p_ble_nus_c, ble_nus_c_evt_t const * p_ble_nus_evt)

// GATT发现事件处理
static void db_disc_handler(ble_db_discovery_evt_t * p_evt)

// NUS客户端初始化
static void nus_c_init(void)

// 数据库发现初始化
static void db_discovery_init(void)

// 处理从设备接收的数据
static void processDataFromDevice(uint8_t * data, uint16_t length)

// 测试NUS客户端功能
void testNUSClient(void)
```

#### 修改的函数：
```cpp
// 修改为使用NUS客户端API
unsigned int sendBluetoothMsgCentral(unsigned char *buf, unsigned short len)

// 添加服务发现启动
static void ble_evt_handler() // 在BLE_GAP_EVT_CONNECTED事件中

// 添加客户端初始化
bool initBle() // 添加db_discovery_init()和nus_c_init()调用
```

### 项目配置更新

#### 添加的源文件：
- `ble_nus_c.c` - NUS客户端实现
- `ble_db_discovery.c` - GATT数据库发现

#### 添加的包含路径：
- `../../../../../../components/ble/ble_db_discovery`

## 使用方法

### 1. 启用Central模式
```cpp
setCentralMode(1);  // 启用Central模式
```

### 2. 检查连接状态
```cpp
bool central_connected = isCentralConnected();
bool nus_discovered = m_nus_service_discovered;
```

### 3. 发送数据
```cpp
uint8_t data[] = "Hello Device!";
uint32_t result = sendBluetoothMsgCentral(data, sizeof(data) - 1);
```

### 4. 发送协议数据
```cpp
uint8_t protocol_data[] = "Protocol message";
uint32_t result = sendDataToDevice(DATA_TYPE_LOCAL_PROCESS, protocol_data, sizeof(protocol_data) - 1);
```

### 5. 测试功能
```cpp
testNUSClient();  // 测试NUS客户端功能
```

## 数据流程

### 发送数据流程：
1. 应用调用 `sendBluetoothMsgCentral()` 或 `sendDataToDevice()`
2. 检查连接状态和服务发现状态
3. 使用 `ble_nus_c_string_send()` 发送数据
4. 数据通过NUS RX特征值发送到Peripheral设备

### 接收数据流程：
1. Peripheral设备通过NUS TX特征值发送通知
2. `ble_nus_c_evt_handler()` 接收 `BLE_NUS_C_EVT_NUS_TX_EVT` 事件
3. 调用 `processDataFromDevice()` 处理数据
4. 通过 `processReceivedData()` 进入数据路由系统
5. 根据数据类型决定转发到手机或本地处理

## 连接流程

1. **扫描阶段**：扫描包含"YRobot"名称的设备
2. **连接阶段**：建立BLE连接
3. **发现阶段**：启动GATT服务发现，查找NUS服务
4. **配置阶段**：获取特征值句柄，启用TX通知
5. **通信阶段**：可以发送和接收数据

## 调试和测试

### 日志输出
- 连接状态：`"BLE Connected as Central"`
- 服务发现：`"Starting GATT service discovery..."`
- NUS发现：`"NUS service discovered on the server."`
- 通知启用：`"Connected to device with Nordic UART Service."`
- 数据接收：`"Received data from NUS TX characteristic."`

### 测试函数
使用 `testNUSClient()` 函数可以：
- 检查Central模式状态
- 检查连接状态
- 检查NUS服务发现状态
- 测试数据发送功能
- 自动启动扫描（如果未连接）

## 注意事项

1. **连接顺序**：必须先建立连接，然后完成服务发现，才能发送数据
2. **数据长度**：单次发送数据长度不能超过MTU限制（通常为244字节）
3. **错误处理**：所有BLE操作都包含错误检查和日志输出
4. **内存管理**：使用Nordic SDK的内存管理机制
5. **线程安全**：所有BLE操作都在主线程中执行

## 兼容性

- 兼容Nordic SDK 16.0.0
- 支持nRF52832芯片
- 兼容S132 SoftDevice v7.0.1
- 与现有的Peripheral模式功能完全兼容
