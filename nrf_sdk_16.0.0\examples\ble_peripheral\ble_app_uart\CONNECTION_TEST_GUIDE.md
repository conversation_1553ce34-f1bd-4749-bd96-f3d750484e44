# BLE连接测试指南

## 当前问题分析

您的代码可以发现设备但连接失败，错误代码`0x12` (NRF_ERROR_INVALID_STATE)。

## 已实施的修复

我已经在您的代码中添加了以下修复：

### 1. 连接前停止扫描
```cpp
// 停止扫描避免状态冲突
nrf_ble_scan_stop();
NRF_LOG_INFO("Stopped scanning, attempting connection...");
```

### 2. 连接状态检查
```cpp
// 检查是否已经连接
if (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) {
  NRF_LOG_WARNING("Already connected as central, ignoring new device");
  return;
}
```

### 3. 详细错误报告
```cpp
if (err_code != NRF_SUCCESS) {
  NRF_LOG_ERROR("sd_ble_gap_connect() failed: 0x%x (%s)", err_code, 
                (err_code == NRF_ERROR_INVALID_STATE) ? "INVALID_STATE" :
                (err_code == NRF_ERROR_INVALID_PARAM) ? "INVALID_PARAM" :
                (err_code == NRF_ERROR_BUSY) ? "BUSY" : "OTHER");
  
  // 重新启动扫描
  scan_start();
}
```

### 4. 超时处理
```cpp
case BLE_GAP_EVT_TIMEOUT:
  if (p_gap_evt->params.timeout.src == BLE_GAP_TIMEOUT_SRC_CONN) {
    NRF_LOG_INFO("Connection timeout - restarting scan");
    if (is_central) {
      scan_start();
    }
  }
  break;
```

## 测试步骤

### 1. 重新编译和烧录

1. 清理项目：Build -> Clean Solution
2. 重新编译：Build -> Rebuild Solution
3. 烧录到设备

### 2. 观察日志输出

成功的连接序列应该是：
```
Starting scanning for devices named: YRobot
Scanning started successfully
Found device: [设备名称]
MAC: [MAC地址]
Stopped scanning, attempting connection...
Connection request sent successfully
BLE Connected as Central
```

### 3. 如果仍然失败

观察新的错误信息：
- 如果仍是`INVALID_STATE`，可能是BLE堆栈配置问题
- 如果是`BUSY`，表示资源冲突
- 如果是`INVALID_PARAM`，表示参数问题

## 进一步调试

### 方法1：使用更宽松的连接参数

如果仍有问题，尝试修改连接参数：

```cpp
// 在scan_evt_handler中使用更宽松的参数
ble_gap_conn_params_t conn_param = {
  .min_conn_interval = MSEC_TO_UNITS(100, UNIT_1_25_MS),  // 100ms
  .max_conn_interval = MSEC_TO_UNITS(200, UNIT_1_25_MS),  // 200ms
  .slave_latency = 0,
  .conn_sup_timeout = MSEC_TO_UNITS(4000, UNIT_10_MS)     // 4s
};
```

### 方法2：添加延迟

在停止扫描和开始连接之间添加短暂延迟：

```cpp
nrf_ble_scan_stop();
nrf_delay_ms(100);  // 100ms延迟
NRF_LOG_INFO("Stopped scanning, attempting connection...");
```

### 方法3：检查目标设备

确保目标设备：
1. 正在广播且可连接
2. 没有连接到其他设备
3. 支持您的连接参数

## 常见解决方案

### 问题：INVALID_STATE错误持续

**解决方案1**：重置BLE堆栈状态
```cpp
// 在main函数中添加
sd_ble_gap_adv_stop();
sd_ble_gap_scan_stop();
nrf_delay_ms(100);
```

**解决方案2**：检查SoftDevice配置
确保在`sdk_config.h`中：
```c
#define NRF_SDH_BLE_CENTRAL_LINK_COUNT 1
#define NRF_SDH_BLE_PERIPHERAL_LINK_COUNT 1
#define NRF_SDH_BLE_TOTAL_LINK_COUNT 2
```

### 问题：连接后立即断开

**解决方案**：添加连接参数更新处理
```cpp
case BLE_GAP_EVT_CONN_PARAM_UPDATE_REQUEST:
  err_code = sd_ble_gap_conn_param_update(p_gap_evt->conn_handle,
                                          &p_gap_evt->params.conn_param_update_request.conn_params);
  APP_ERROR_CHECK(err_code);
  break;
```

## 测试用的简化版本

如果问题仍然存在，可以尝试这个最简化的连接逻辑：

```cpp
static void simple_scan_evt_handler(scan_evt_t const * p_scan_evt) {
  static bool connection_attempted = false;
  
  if (p_scan_evt->scan_evt_id == NRF_BLE_SCAN_EVT_FILTER_MATCH && !connection_attempted) {
    connection_attempted = true;
    
    ble_gap_evt_adv_report_t const * p_adv = p_scan_evt->params.filter_match.p_adv_report;
    
    NRF_LOG_INFO("Attempting connection to first device found");
    
    // 停止扫描
    nrf_ble_scan_stop();
    
    // 使用默认连接参数
    ret_code_t err_code = sd_ble_gap_connect(&p_adv->peer_addr,
                                             p_scan_evt->p_scan_params,
                                             NULL,  // 使用默认参数
                                             APP_BLE_CONN_CFG_TAG);
    
    NRF_LOG_INFO("Connection result: 0x%x", err_code);
  }
}
```

## 验证连接成功

连接成功后，您应该能够：

1. **看到连接日志**：`BLE Connected as Central`
2. **发送数据**：使用`sendBluetoothMsgCentral()`
3. **接收数据**：在`nus_data_handler`中处理

## 下一步

1. **立即测试**：重新编译并观察新的日志输出
2. **如果成功**：测试数据传输功能
3. **如果失败**：尝试简化版本或检查硬件
4. **报告结果**：提供新的日志输出以便进一步分析

记住：连接问题通常是状态管理或时序问题，修复后的代码应该能解决大部分情况。
