# NUS Client 测试指南

## 快速测试步骤

### 1. 编译和烧录
```bash
# 在SES中打开项目
# 编译项目（确保没有编译错误）
# 烧录到nRF52832设备
```

### 2. 启用Central模式
```cpp
// 在代码中设置
setCentralMode(1);

// 或者通过串口命令（如果实现了命令接口）
```

### 3. 准备测试环境

#### 需要的设备：
1. **主设备**：运行我们修改后的代码（Central + Peripheral）
2. **目标设备**：运行标准NUS Peripheral代码，广播名称包含"YRobot"
3. **手机**：运行nRF Connect或类似BLE调试应用

#### 设置目标设备：
- 确保目标设备广播名称包含"YRobot"
- 确保目标设备运行Nordic UART Service
- 目标设备应该可以被扫描发现

### 4. 测试连接流程

#### 步骤1：启动扫描
```
预期日志：
"Initializing Central mode"
"Starting scan..."
"<PERSON><PERSON> started"
```

#### 步骤2：发现目标设备
```
预期日志：
"Found device: YRobot_xxxx"
"Device address: xx:xx:xx:xx:xx:xx"
"Attempting to connect..."
```

#### 步骤3：建立连接
```
预期日志：
"BLE Connected as Central"
"Starting GATT service discovery..."
```

#### 步骤4：服务发现
```
预期日志：
"NUS service discovered on the server."
"Connected to device with Nordic UART Service."
```

### 5. 测试数据通信

#### 使用测试函数：
```cpp
// 调用测试函数
testNUSClient();
```

#### 预期输出：
```
=== Testing NUS Client Functionality ===
Central mode enabled: YES
Central connected: YES
NUS service discovered: YES
NUS Client test data sent successfully
Protocol data sent successfully
=== NUS Client Test Complete ===
```

#### 手动发送数据：
```cpp
uint8_t test_data[] = "Hello from Central!";
uint32_t result = sendBluetoothMsgCentral(test_data, sizeof(test_data) - 1);
```

### 6. 测试数据接收

#### 从目标设备发送数据：
- 在目标设备上发送数据到NUS TX特征值
- 观察主设备的日志输出

#### 预期日志：
```
"Received data from NUS TX characteristic."
"Processing data from device, length: xx"
"Raw data from device - processing locally"
```

### 7. 测试数据路由

#### 测试转发功能：
```cpp
// 如果同时连接了手机和设备
testForwarding();
```

#### 预期行为：
- 从手机接收的数据可以转发到设备
- 从设备接收的数据可以转发到手机
- 本地处理的数据正确解析

## 故障排除

### 常见问题1：无法发现目标设备
**症状**：扫描启动但找不到目标设备
**解决方案**：
1. 检查目标设备是否正在广播
2. 检查广播名称是否包含"YRobot"
3. 检查扫描过滤器设置
4. 增加扫描时间

### 常见问题2：连接失败
**症状**：发现设备但连接失败
**解决方案**：
1. 检查目标设备是否接受连接
2. 检查连接参数设置
3. 确保目标设备没有达到最大连接数
4. 重启目标设备

### 常见问题3：服务发现失败
**症状**：连接成功但找不到NUS服务
**解决方案**：
1. 确认目标设备实现了NUS服务
2. 检查服务UUID是否正确
3. 增加发现超时时间
4. 检查GATT数据库

### 常见问题4：无法发送数据
**症状**：连接和发现成功，但发送数据失败
**解决方案**：
1. 检查 `m_nus_service_discovered` 标志
2. 确认RX特征值句柄正确
3. 检查数据长度是否超过MTU
4. 验证目标设备的RX特征值属性

### 常见问题5：无法接收数据
**症状**：可以发送但接收不到数据
**解决方案**：
1. 确认TX通知已启用
2. 检查CCCD配置
3. 验证目标设备是否发送通知
4. 检查事件处理函数

## 调试技巧

### 1. 启用详细日志
```cpp
// 在sdk_config.h中设置
#define NRF_LOG_LEVEL 4  // Debug level
#define BLE_NUS_C_LOG_ENABLED 1
#define BLE_DB_DISCOVERY_LOG_ENABLED 1
```

### 2. 使用nRF Connect监控
- 连接到目标设备
- 监控服务和特征值
- 验证通知配置
- 测试数据发送/接收

### 3. 检查连接参数
```cpp
// 在连接事件中添加日志
NRF_LOG_INFO("Connection interval: %d", conn_params.min_conn_interval);
NRF_LOG_INFO("Slave latency: %d", conn_params.slave_latency);
NRF_LOG_INFO("Supervision timeout: %d", conn_params.conn_sup_timeout);
```

### 4. 监控内存使用
```cpp
// 检查可用内存
uint32_t ram_start = 0;
uint32_t ram_size = 0;
sd_ble_enable(&ram_start);
NRF_LOG_INFO("Required RAM start: 0x%08X", ram_start);
```

## 性能测试

### 1. 连接时间测试
- 记录从扫描开始到服务发现完成的时间
- 目标：< 10秒

### 2. 数据吞吐量测试
- 连续发送数据包
- 测量成功率和延迟
- 目标：> 95% 成功率

### 3. 稳定性测试
- 长时间运行（24小时）
- 多次连接/断开循环
- 内存泄漏检测

## 验收标准

### 基本功能：
- ✅ 能够扫描并发现目标设备
- ✅ 能够建立BLE连接
- ✅ 能够发现NUS服务
- ✅ 能够启用通知
- ✅ 能够发送数据
- ✅ 能够接收数据

### 集成功能：
- ✅ 与现有Peripheral模式兼容
- ✅ 数据路由功能正常
- ✅ 协议数据包处理正确
- ✅ 错误处理完善

### 性能要求：
- ✅ 连接时间 < 10秒
- ✅ 数据传输成功率 > 95%
- ✅ 内存使用稳定
- ✅ 无内存泄漏
