# BLE Central and Peripheral Mode Implementation

This document describes the implementation of simultaneous BLE Central and Peripheral functionality in the ble_app_uart example.

## Overview

The application now supports both BLE Central and Peripheral modes simultaneously:
- **Peripheral Mode**: Acts as a BLE peripheral, advertising and accepting connections from central devices
- **Central Mode**: Acts as a BLE central, scanning for and connecting to other peripheral devices

## Key Features

1. **Dual Mode Operation**: Device can operate as both central and peripheral simultaneously
2. **Dynamic Control**: Central mode can be enabled/disabled via the `is_central` variable
3. **Multiple Connections**: Supports one peripheral connection and one central connection
4. **Automatic Reconnection**: Central mode automatically restarts scanning after disconnection

## Configuration

### Enabling Central Mode

In `main.cpp`, modify the `setCentralMode()` call in the `main()` function:

```cpp
// Enable both central and peripheral modes
setCentralMode(true);

// Enable only peripheral mode (original behavior)
setCentralMode(false);
```

### Target Device Name

The central mode scans for devices with the name "YRobot". To change this, modify the `m_target_periph_name` variable:

```cpp
static char m_target_periph_name[] = "YourDeviceName";
```

## API Functions

### Control Functions

- `setCentralMode(bool enable)` - Enable/disable central mode
- `getCentralMode()` - Get current central mode status

### Connection Status

- `isCentralConnected()` - Check if central connection is active
- `isPeripheralConnected()` - Check if peripheral connection is active

### Data Transmission

- `sendBluetoothMsgProxy(buf, len)` - Send data via peripheral connection (original function)
- `sendBluetoothMsgCentral(buf, len)` - Send data via central connection

## Implementation Details

### Connection Handles

- `m_conn_handle` - Handle for peripheral connection (when acting as peripheral)
- `m_central_conn_handle` - Handle for central connection (when acting as central)

### Event Handling

The BLE event handler (`ble_evt_handler`) now distinguishes between:
- Peripheral connections (role = BLE_GAP_ROLE_PERIPH)
- Central connections (role = BLE_GAP_ROLE_CENTRAL)

### Scanning Configuration

- **Scan Filter**: Filters devices by name
- **Auto-reconnect**: Automatically restarts scanning after central disconnection
- **Scan Parameters**: Uses default scanning parameters from nRF BLE Scan module

## Usage Example

```cpp
// Check if central mode is enabled
if (getCentralMode()) {
    // Check connection status
    if (isCentralConnected()) {
        // Send data to connected peripheral device
        uint8_t data[] = "Hello from Central";
        sendBluetoothMsgCentral(data, sizeof(data));
    }
    
    if (isPeripheralConnected()) {
        // Send data to connected central device
        uint8_t data[] = "Hello from Peripheral";
        sendBluetoothMsgProxy(data, sizeof(data));
    }
}
```

## Log Messages

The implementation provides detailed logging:
- Central mode enable/disable status
- Connection events for both roles
- Scanning start/stop events
- Device discovery events

## Notes

1. The device always operates in peripheral mode (advertising)
2. Central mode is optional and controlled by the `is_central` variable
3. Both connections can be active simultaneously
4. The implementation uses the nRF BLE Scan module for central functionality
5. Connection parameters are shared between central and peripheral modes

## Troubleshooting

1. **Central not connecting**: Check that target device name matches `m_target_periph_name`
2. **Scanning not starting**: Verify that `setCentralMode(true)` is called before `initBle()`
3. **Multiple connections**: Each role maintains its own connection handle
