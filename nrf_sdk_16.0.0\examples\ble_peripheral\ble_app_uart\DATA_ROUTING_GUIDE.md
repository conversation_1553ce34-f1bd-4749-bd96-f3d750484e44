# BLE Central+Peripheral Data Routing Guide

## 概述

这个增强版的BLE应用程序现在支持同时作为Central和Peripheral工作，并实现了智能数据路由功能。设备A和设备B使用相同的代码，通过`is_central`变量来区分角色。

## 主要功能

### 1. 双重连接模式
- **Peripheral模式**: 可以被手机连接
- **Central模式**: 可以连接到其他设备（如设备B）
- 同时支持两种连接，实现数据桥接功能

### 2. 智能数据路由

#### 数据协议格式
```
[Header: 0xAA] [Data Type: 1 byte] [Length: 2 bytes] [Data: N bytes]
```

#### 数据类型定义
- `DATA_TYPE_FORWARD_TO_PHONE (0x01)`: 转发到手机
- `DATA_TYPE_FORWARD_TO_DEVICE (0x02)`: 转发到其他设备
- `DATA_TYPE_LOCAL_PROCESS (0x03)`: 本地处理
- `DATA_TYPE_HEARTBEAT (0x04)`: 心跳消息
- `DATA_TYPE_STATUS (0x05)`: 状态信息

### 3. 数据路由逻辑

#### 来自手机的数据 (DATA_SOURCE_PHONE)
1. **有协议头**: 根据数据类型决定处理方式
   - `FORWARD_TO_DEVICE`: 转发到连接的设备
   - `LOCAL_PROCESS`: 本地处理
   - `HEARTBEAT/STATUS`: 相应处理

2. **无协议头**: 默认本地处理，可选择转发到设备

#### 来自设备的数据 (DATA_SOURCE_DEVICE)
1. **有协议头**: 根据数据类型决定处理方式
   - `FORWARD_TO_PHONE`: 转发到手机
   - `LOCAL_PROCESS`: 本地处理
   - `HEARTBEAT/STATUS`: 相应处理

2. **无协议头**: 默认本地处理，可选择转发到手机

## 主要API函数

### 连接管理
```c
void setCentralMode(int enable);           // 启用/禁用Central模式
int getCentralMode(void);                  // 获取Central模式状态
int isCentralConnected(void);              // 检查Central连接状态
int isPeripheralConnected(void);           // 检查Peripheral连接状态
void disconnect_all_connections(void);     // 断开所有连接
```

### 数据发送
```c
// 发送协议数据
unsigned int sendDataToPhone(uint8_t data_type, uint8_t *data, uint16_t data_len);
unsigned int sendDataToDevice(uint8_t data_type, uint8_t *data, uint16_t data_len);

// 发送原始数据
unsigned int sendBluetoothMsgPeripheral(unsigned char *buf, unsigned short len);
unsigned int sendBluetoothMsgCentral(unsigned char *buf, unsigned short len);

// 系统消息
void sendHeartbeat(void);                  // 发送心跳
void sendStatusInfo(void);                 // 发送状态信息
```

### 测试函数
```c
void testCentralPeripheralMode(void);      // 测试基本连接功能
void testDataRouting(void);                // 测试数据路由功能
void testForwarding(void);                 // 测试数据转发功能
```

## 使用示例

### 1. 启用Central+Peripheral模式
```c
// 在main函数中设置
setCentralMode(true);  // 启用Central模式，Peripheral模式默认启用
```

### 2. 发送数据到手机
```c
uint8_t message[] = "Hello Phone!";
sendDataToPhone(DATA_TYPE_LOCAL_PROCESS, message, simple_strlen((char*)message));
```

### 3. 发送数据到设备
```c
uint8_t message[] = "Hello Device!";
sendDataToDevice(DATA_TYPE_LOCAL_PROCESS, message, simple_strlen((char*)message));
```

### 4. 转发数据
```c
// 从手机收到的数据转发到设备
uint8_t forward_data[] = "Forward to device";
sendDataToDevice(DATA_TYPE_FORWARD_TO_DEVICE, forward_data, simple_strlen((char*)forward_data));

// 从设备收到的数据转发到手机
uint8_t forward_data2[] = "Forward to phone";
sendDataToPhone(DATA_TYPE_FORWARD_TO_PHONE, forward_data2, simple_strlen((char*)forward_data2));
```

## 自动功能

### 1. 定期任务
- **心跳**: 每30秒发送一次心跳消息
- **状态**: 每60秒发送一次状态信息
- **重连**: Central模式下每5秒尝试重连

### 2. 连接监控
- 自动监控连接质量
- 连接断开时自动重连（Central模式）
- 连接状态日志记录

## 配置说明

### 设备角色配置
- **设备A**: `is_central = true` - 同时作为Central和Peripheral
- **设备B**: `is_central = false` - 仅作为Peripheral（可根据需要修改）

### 目标设备名称
```c
static char m_target_periph_name[] = "YRobot_FE:8E:36:B5:36:D9";
```
修改此变量以连接到不同的目标设备。

## 调试和监控

### 日志输出
- 所有数据路由操作都有详细的日志输出
- 连接状态变化会被记录
- 错误情况会有相应的错误日志

### 状态检查
```c
log_ble_state();  // 输出当前BLE连接状态
```

## 注意事项

1. **MTU限制**: 数据包大小限制为244字节（考虑MTU限制）
2. **连接数量**: 同时支持一个Central连接和一个Peripheral连接
3. **内存使用**: 协议头增加4字节开销
4. **错误处理**: 所有API函数都返回错误码，需要检查返回值

## 故障排除

1. **连接失败**: 检查目标设备名称和MAC地址
2. **数据发送失败**: 检查连接状态和数据长度
3. **路由错误**: 检查数据类型和协议头格式
4. **内存不足**: 检查RAM配置和连接参数

这个系统提供了完整的BLE数据桥接功能，支持智能路由和自动管理，适用于复杂的多设备通信场景。
