# BLE连接失败深度分析

## 当前状态分析

从您的日志可以看出：

### ✅ 已解决的问题
1. **设备名称解析**：成功解析"YRobot_FE:8E:36:B5:36:D9"
2. **设备发现**：能够正确发现目标设备
3. **信号强度**：RSSI -50/-51 dBm，信号良好
4. **扫描停止**：成功停止扫描 (result: 0x0)

### ❌ 仍存在的问题
**所有连接尝试都失败**：
- 第一次：`0x12` (NRF_ERROR_INVALID_STATE)
- 第二次：`0x10` (NRF_ERROR_INVALID_PARAM)
- 第三次：`0x12` (NRF_ERROR_INVALID_STATE)

## 错误代码分析

### NRF_ERROR_INVALID_STATE (0x12)
**可能原因**：
1. BLE堆栈状态不正确
2. 已有连接操作在进行中
3. SoftDevice状态冲突

### NRF_ERROR_INVALID_PARAM (0x10)
**可能原因**：
1. 连接参数不被目标设备接受
2. 扫描参数无效
3. 地址类型不匹配

## 新增的诊断功能

我已经添加了以下改进：

### 1. 广播类型检查
```cpp
NRF_LOG_INFO("Adv type: 0x%02X, Connectable: %s", 
             p_adv->type.connectable, 
             p_adv->type.connectable ? "YES" : "NO");
```

### 2. BLE状态重置
```cpp
// 重置BLE状态
sd_ble_gap_adv_stop();  // 停止广播
nrf_delay_ms(100);
advertising_start(false);  // 重新启动广播
```

### 3. 四重连接尝试策略
1. **快速参数**：7.5-30ms连接间隔
2. **NULL参数**：让SoftDevice选择
3. **最小参数**：20-40ms连接间隔
4. **原始参数**：使用SDK默认参数

### 4. 更详细的错误分析
包含设备可连接性检查和状态重置。

## 可能的根本原因

### 原因1：目标设备问题
**现象**：所有连接尝试都失败
**可能性**：
- 目标设备已连接到其他设备
- 目标设备不接受新连接
- 目标设备连接队列已满

**验证方法**：
1. 用手机nRF Connect尝试连接同一设备
2. 重启目标设备
3. 确认目标设备支持多连接

### 原因2：地址类型问题
**现象**：INVALID_PARAM错误
**可能性**：
- 公共地址 vs 随机地址类型不匹配
- 地址解析问题

**解决方案**：
检查地址类型：
```cpp
NRF_LOG_INFO("Address type: %d", p_adv->peer_addr.addr_type);
```

### 原因3：SoftDevice配置问题
**现象**：INVALID_STATE错误
**可能性**：
- 连接数量限制
- 内存分配不足
- 事件处理冲突

## 测试建议

### 测试1：验证目标设备
1. **使用手机测试**：
   - 安装nRF Connect
   - 扫描并尝试连接"YRobot_FE:8E:36:B5:36:D9"
   - 检查连接是否成功

2. **检查设备状态**：
   - 确认目标设备正在广播
   - 确认设备接受连接
   - 重启目标设备清除连接状态

### 测试2：简化测试环境
1. **使用标准peripheral**：
   - 用另一个nRF52运行标准ble_app_uart
   - 设置设备名为"YRobot"
   - 测试连接是否成功

2. **关闭其他BLE设备**：
   - 关闭附近其他BLE设备
   - 减少RF干扰

### 测试3：修改配置
1. **检查sdk_config.h**：
```c
#define NRF_SDH_BLE_CENTRAL_LINK_COUNT 1
#define NRF_SDH_BLE_PERIPHERAL_LINK_COUNT 1
#define NRF_SDH_BLE_TOTAL_LINK_COUNT 2
```

2. **增加连接超时**：
```cpp
.conn_sup_timeout = MSEC_TO_UNITS(6000, UNIT_10_MS)  // 6秒
```

## 预期的新日志输出

重新测试后应该看到：

```
Adv type: 0xXX, Connectable: YES
Stopped scanning (result: 0x0), attempting connection...
Connection params: min=6, max=24, latency=0, timeout=400
Attempting connection with custom parameters...
```

然后可能的结果：
- **成功**：`Connection request sent successfully` + `BLE Connected as Central`
- **失败**：继续尝试其他方法

## 如果仍然失败

### 方案A：临时禁用连接，只做发现
```cpp
// 在scan_evt_handler中注释掉连接代码
NRF_LOG_INFO("Device discovered: %s - SKIPPING CONNECTION FOR TEST", dev_name);
// 不尝试连接，继续扫描
```

### 方案B：检查硬件
1. **天线连接**：确认天线正确连接
2. **供电稳定**：检查电源稳定性
3. **RF环境**：在不同位置测试

### 方案C：使用不同的SDK示例
1. 尝试使用`ble_app_multilink_central`示例
2. 对比连接实现差异
3. 移植工作代码

## 成功连接的标志

当连接成功时，应该看到：
```
Adv type: 0xXX, Connectable: YES
Manual name extraction: YRobot_FE:8E:36:B5:36:D9 (type: 0x09, len: 24)
Found device: YRobot_FE:8E:36:B5:36:D9 (name_found: 1)
Stopped scanning (result: 0x0), attempting connection...
Attempting connection with custom parameters...
Connection request sent successfully
BLE Connected as Central
```

## 下一步行动

1. **立即测试**：重新编译并观察新的诊断信息
2. **手机验证**：用nRF Connect测试目标设备
3. **简化测试**：如果仍失败，先测试标准peripheral设备
4. **报告结果**：提供新的完整日志和手机测试结果

这次的改进应该能提供更多诊断信息，帮助确定连接失败的确切原因。
