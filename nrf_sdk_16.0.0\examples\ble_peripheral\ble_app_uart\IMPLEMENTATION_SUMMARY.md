# BLE Central and Peripheral Implementation Summary

## Overview

Successfully implemented simultaneous BLE Central and Peripheral functionality in the `ble_app_uart` example. The device can now operate as both a BLE central (scanning and connecting to other devices) and a BLE peripheral (advertising and accepting connections) at the same time.

## Files Modified/Added

### Modified Files

1. **main.cpp** - Main implementation file
   - Added central mode variables and functions
   - Enhanced BLE event handling for dual mode operation
   - Added connection management for both roles

### New Files

1. **ble_central_peripheral.h** - Header file with function declarations
2. **README_CENTRAL_PERIPHERAL.md** - Detailed documentation
3. **example_usage.cpp** - Usage examples and integration patterns
4. **IMPLEMENTATION_SUMMARY.md** - This summary document

## Key Features Implemented

### 1. Dual Mode Operation
- Device operates as both central and peripheral simultaneously
- Independent connection handles for each role
- Automatic scanning restart after central disconnection

### 2. Dynamic Control
- `is_central` variable controls central mode enable/disable
- Runtime functions to check and modify central mode status
- Configurable target device name for scanning

### 3. Connection Management
- Separate connection handles: `m_conn_handle` (peripheral), `m_central_conn_handle` (central)
- Connection status checking functions
- Proper cleanup on disconnection

### 4. Data Transmission
- Original peripheral transmission function preserved
- New central transmission function added
- Error handling and validation

## API Functions Added

### Control Functions
```cpp
void setCentralMode(bool enable);        // Enable/disable central mode
bool getCentralMode(void);               // Get central mode status
```

### Connection Status
```cpp
bool isCentralConnected(void);           // Check central connection
bool isPeripheralConnected(void);        // Check peripheral connection
```

### Data Transmission
```cpp
uint32_t sendBluetoothMsgCentral(uint8_t *buf, uint16_t len);  // Send via central
uint32_t sendBluetoothMsgProxy(uint8_t *buf, uint16_t len);    // Send via peripheral (existing)
```

### Testing
```cpp
void testCentralPeripheralMode(void);    // Test function for demonstration
```

## Implementation Details

### Central Mode Variables
```cpp
NRF_BLE_SCAN_DEF(m_scan);                           // Scanning module instance
static bool is_central = false;                     // Central mode flag
static char m_target_periph_name[] = "YRobot";     // Target device name
static uint16_t m_central_conn_handle = BLE_CONN_HANDLE_INVALID;  // Central connection handle
```

### Enhanced BLE Event Handling
- `BLE_GAP_EVT_CONNECTED`: Distinguishes between central and peripheral connections
- `BLE_GAP_EVT_DISCONNECTED`: Handles disconnection for both roles
- `BLE_GAP_EVT_ADV_REPORT`: Handled by scanning module

### Scanning Configuration
- Name-based filtering for target devices
- Automatic connection on filter match
- Default scanning parameters from nRF BLE Scan module

## Usage Instructions

### 1. Enable Central Mode
In `main()` function:
```cpp
setCentralMode(true);  // Enable both central and peripheral modes
```

### 2. Check Connection Status
```cpp
if (isCentralConnected()) {
    // Central connection is active
}

if (isPeripheralConnected()) {
    // Peripheral connection is active
}
```

### 3. Send Data
```cpp
// Send via central connection
uint8_t data[] = "Hello from Central";
sendBluetoothMsgCentral(data, sizeof(data));

// Send via peripheral connection (original method)
uint8_t data[] = "Hello from Peripheral";
sendBluetoothMsgProxy(data, sizeof(data));
```

### 4. Change Target Device
Modify the target device name:
```cpp
static char m_target_periph_name[] = "YourDeviceName";
```

## Configuration Options

### Central Mode Control
- Set `setCentralMode(true)` to enable central functionality
- Set `setCentralMode(false)` for peripheral-only operation (original behavior)

### Target Device Selection
- Modify `m_target_periph_name` to scan for specific device names
- Uses name-based filtering for device discovery

### Connection Parameters
- Shared connection parameters between central and peripheral modes
- Configurable via existing parameter definitions

## Testing and Validation

### Test Function
The `testCentralPeripheralMode()` function provides:
- Status logging for both modes
- Connection state reporting
- Data transmission testing
- Error code reporting

### Log Messages
Comprehensive logging for:
- Central mode enable/disable events
- Connection/disconnection events for both roles
- Scanning start/stop events
- Device discovery events
- Data transmission results

## Integration Notes

### Backward Compatibility
- All existing peripheral functionality preserved
- Original API functions unchanged
- Existing applications continue to work without modification

### Memory Usage
- Minimal additional memory overhead
- Uses existing BLE stack resources
- Scanning module adds ~1KB RAM usage

### Performance Considerations
- Simultaneous connections may affect throughput
- Scanning activity uses additional radio time
- Connection intervals shared between roles

## Error Handling

### Connection Failures
- Automatic scanning restart on central disconnection
- Proper cleanup of connection handles
- Error code propagation for debugging

### Invalid State Handling
- Validation of connection handles before transmission
- Graceful handling of disabled central mode
- Resource availability checking

## Future Enhancements

### Possible Improvements
1. Multiple central connections support
2. Dynamic target device list
3. Connection priority management
4. Advanced filtering options
5. Automatic role switching based on conditions

### Configuration Extensions
1. Runtime scanning parameter modification
2. Connection interval optimization per role
3. Power management for dual mode operation
4. Custom connection event handling

## Conclusion

The implementation successfully adds central functionality while maintaining full backward compatibility. The device can now operate in three modes:

1. **Peripheral Only** (original behavior) - `setCentralMode(false)`
2. **Central + Peripheral** (new dual mode) - `setCentralMode(true)`
3. **Dynamic Switching** - Runtime control via API functions

The implementation is robust, well-documented, and provides comprehensive error handling and logging for easy debugging and integration.
