# 数据转发问题修复指南

## 问题描述

从手机发送数据到主从一体设备，设备尝试转发给peripheral时失败：

```
<info> app: Received data from phone route:[1]
<info> app:  01 02 03 04 05 06 07   |....... 
<error> nrf_ble_gq: SD GATT procedure (1) failed on connection handle 0 with error: 0x00000013.
<error> app: NUS Client error: 0x13
<info> app: sent data to device successfully  ← 这个日志是误导性的
```

## 错误分析

### 错误代码 0x13 = NRF_ERROR_INVALID_STATE

这个错误表明：
1. **连接状态不稳定**：虽然连接句柄有效，但连接可能处于不稳定状态
2. **GATT操作冲突**：可能有其他GATT操作正在进行
3. **服务未完全准备**：NUS服务发现完成但特征值可能未完全准备
4. **通知未启用**：TX特征值的通知可能未正确启用

## 已实施的修复

### 1. 增强状态检查

**修复前**：
```c
unsigned int sendBluetoothMsgCentral(unsigned char *buf, unsigned short len) {
  if (m_central_conn_handle != BLE_CONN_HANDLE_INVALID && m_nus_service_discovered) {
    uint16_t length = len;
    return ble_nus_c_string_send(&m_ble_nus_c, buf, length);
  }
  return NRF_ERROR_INVALID_STATE;
}
```

**修复后**：
```c
unsigned int sendBluetoothMsgCentral(unsigned char *buf, unsigned short len) {
  // 详细的状态检查
  if (m_central_conn_handle == BLE_CONN_HANDLE_INVALID) {
    NRF_LOG_ERROR("Central connection handle invalid");
    return NRF_ERROR_INVALID_STATE;
  }
  
  if (!m_nus_service_discovered) {
    NRF_LOG_ERROR("NUS service not discovered yet");
    return NRF_ERROR_INVALID_STATE;
  }
  
  // 检查NUS客户端状态
  if (m_ble_nus_c.conn_handle != m_central_conn_handle) {
    NRF_LOG_ERROR("NUS client connection handle mismatch");
    return NRF_ERROR_INVALID_STATE;
  }
  
  // 检查句柄有效性
  if (m_ble_nus_c.handles.nus_rx_handle == BLE_GATT_HANDLE_INVALID) {
    NRF_LOG_ERROR("NUS RX handle invalid");
    return NRF_ERROR_INVALID_STATE;
  }
  
  // 发送数据
  uint32_t err_code = ble_nus_c_string_send(&m_ble_nus_c, buf, length);
  if (err_code != NRF_SUCCESS) {
    NRF_LOG_ERROR("ble_nus_c_string_send failed: 0x%08X", err_code);
  }
  
  return err_code;
}
```

### 2. 改进错误处理

**新增详细错误分析**：
```c
static void nus_c_error_handler(uint32_t nrf_error) {
    switch (nrf_error) {
        case NRF_ERROR_INVALID_STATE:
            NRF_LOG_ERROR("Invalid state - connection unstable or GATT operation in progress");
            break;
        case NRF_ERROR_RESOURCES:
            NRF_LOG_ERROR("No resources - GATT queue may be full");
            break;
        // ... 其他错误类型
    }
}
```

### 3. 添加传输准备检查

**新增函数**：
```c
bool isCentralReadyForTransmission(void) {
    return (m_central_conn_handle != BLE_CONN_HANDLE_INVALID) &&
           m_nus_service_discovered &&
           (m_ble_nus_c.conn_handle == m_central_conn_handle) &&
           (m_ble_nus_c.handles.nus_rx_handle != BLE_GATT_HANDLE_INVALID);
}
```

### 4. 改进数据路由逻辑

**修复前**：
```c
if (is_central) {
  uint32_t err_code = sendBluetoothMsgCentral(data, length);
  if (err_code == NRF_SUCCESS) {
    NRF_LOG_INFO("sent data to device successfully");
  }
}
```

**修复后**：
```c
if (is_central) {
  if (!isCentralReadyForTransmission()) {
    NRF_LOG_ERROR("Central connection not ready for transmission");
    return;
  }
  
  uint32_t err_code = sendBluetoothMsgCentral(data, length);
  if (err_code == NRF_SUCCESS) {
    NRF_LOG_INFO("sent data to device successfully");
  } else {
    NRF_LOG_ERROR("Failed to send data to device: 0x%08X", err_code);
    // 提供错误建议
  }
}
```

## 诊断工具

### 新增诊断函数

```c
void diagnoseCentralConnection(void);
```

**使用方法**：
```c
// 在RTT Viewer或调试器中调用
diagnoseCentralConnection();
```

**输出示例**：
```
<info> app: === Central Connection Diagnosis ===
<info> app: Central mode enabled: YES
<info> app: Central connection handle: 0x0000
<info> app: Central connected: YES
<info> app: NUS service discovered: YES
<info> app: NUS client connection handle: 0x0000
<info> app: NUS RX handle: 0x000D
<info> app: NUS TX handle: 0x000F
<info> app: NUS TX CCCD handle: 0x0010
<info> app: Ready for transmission: YES
<info> app: === Diagnosis Complete ===
```

## 故障排除步骤

### 1. 立即诊断
```c
diagnoseCentralConnection();
```

### 2. 检查关键状态
- Central连接句柄是否有效
- NUS服务是否已发现
- NUS客户端句柄是否匹配
- RX句柄是否有效

### 3. 常见问题及解决方案

#### 问题：NUS client connection handle mismatch
**原因**：NUS客户端的连接句柄与Central连接句柄不匹配
**解决**：重新连接或重新初始化NUS客户端

#### 问题：NUS RX handle invalid
**原因**：服务发现不完整或CCCD问题
**解决**：检查CCCD配置，确保服务发现完整

#### 问题：GATT queue full (NRF_ERROR_RESOURCES)
**原因**：GATT队列满了
**解决**：减少数据发送频率或增加队列大小

#### 问题：Connection unstable
**原因**：连接参数不合适或信号质量差
**解决**：调整连接参数或改善信号环境

## 测试验证

### 1. 基本连接测试
```c
// 检查连接状态
diagnoseCentralConnection();

// 测试数据发送
if (isCentralReadyForTransmission()) {
    uint8_t test_data[] = "Test message";
    uint32_t result = sendBluetoothMsgCentral(test_data, sizeof(test_data) - 1);
    NRF_LOG_INFO("Test send result: 0x%X", result);
}
```

### 2. 数据路由测试
```c
// 模拟从手机接收数据并转发
uint8_t phone_data[] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07};
// 设置路由为转发模式
// 发送数据并观察日志
```

## 预期修复效果

### 修复前的错误日志：
```
<error> nrf_ble_gq: SD GATT procedure (1) failed on connection handle 0 with error: 0x00000013.
<error> app: NUS Client error: 0x13
<info> app: sent data to device successfully  ← 误导性
```

### 修复后的正常日志：
```
<debug> app: Forwarding 7 bytes from phone to peripheral device
<debug> app: Sending 7 bytes to central device (handle: 0x0000)
<info> app: sent data to device successfully
```

### 修复后的错误日志（如果仍有问题）：
```
<error> app: Central connection not ready for transmission
<error> app: NUS service not discovered yet
<warning> app: Suggestion: Check if peripheral device is still connected and NUS service is ready
```

## 最佳实践

1. **发送前检查**：始终在发送数据前检查连接状态
2. **错误处理**：为每种错误类型提供具体的处理建议
3. **状态监控**：定期检查连接和服务状态
4. **日志分析**：使用详细日志跟踪问题
5. **重试机制**：对于临时错误实施重试机制

## 后续优化建议

1. **添加重试机制**：对于 `NRF_ERROR_BUSY` 和 `NRF_ERROR_RESOURCES` 错误
2. **连接质量监控**：监控RSSI和连接参数
3. **自动恢复**：检测到连接问题时自动重连
4. **流控制**：实施数据发送流控制机制
