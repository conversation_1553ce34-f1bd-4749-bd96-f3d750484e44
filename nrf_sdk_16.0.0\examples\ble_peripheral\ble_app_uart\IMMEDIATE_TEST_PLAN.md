# 立即测试计划

## 新增的调试功能

我已经为您的代码添加了强大的调试功能：

### 1. 原始广播数据显示
现在会显示完整的广播数据内容，帮助分析设备名称问题

### 2. BLE状态详细检查
每次扫描前会显示当前的连接状态

### 3. 双重连接尝试策略
- 首先尝试自定义连接参数
- 失败后自动尝试默认参数

### 4. 更详细的错误信息
包含扫描停止结果和连接参数详情

## 立即测试步骤

### 步骤1：重新编译
```
Build -> Clean Solution
Build -> Rebuild Solution
```

### 步骤2：烧录并观察日志

您现在应该看到更详细的输出：

```
=== BLE State Check ===
Central handle: 0xFFFF
Peripheral handle: 0xFFFF  
Central mode enabled: YES
=====================
Starting scanning for devices named: YRobot
Scanning started successfully

Raw adv data (XX bytes):
  [00]: 0xXX
  [01]: 0xXX
  [02]: 0xXX
  ...

Found device: [设备名称] (name_found: 1)
MAC: CB:A8:54:52:CC:6C
RSSI: -XX dBm, Data len: XX
Stopped scanning (result: 0x00), attempting connection...
Connection params: min=80, max=160, latency=0, timeout=400
```

### 步骤3：分析结果

#### 情况A：连接成功
如果看到：
```
Connection request sent successfully
BLE Connected as Central
```
恭喜！问题已解决。

#### 情况B：第一次连接失败，第二次成功
如果看到：
```
sd_ble_gap_connect() with custom params failed: 0xXX
Retrying with default connection parameters...
Connection request with defaults sent successfully
BLE Connected as Central
```
说明是连接参数问题，已通过默认参数解决。

#### 情况C：两次连接都失败
如果看到：
```
sd_ble_gap_connect() with defaults also failed: 0xXX (INVALID_STATE)
```
需要进一步分析根本原因。

## 关键诊断信息

### 1. 原始广播数据分析

观察原始数据输出，寻找：
- **设备名称字段**：通常以0x09（完整名称）或0x08（短名称）开头
- **数据格式**：第一个字节是长度，第二个字节是类型
- **编码问题**：检查是否有非ASCII字符

示例正常的广播数据：
```
Raw adv data (31 bytes):
  [00]: 0x02  // 长度
  [01]: 0x01  // 类型：Flags
  [02]: 0x06  // 值
  [03]: 0x07  // 长度
  [04]: 0x09  // 类型：Complete Local Name
  [05]: 0x59  // 'Y'
  [06]: 0x52  // 'R'
  [07]: 0x6F  // 'o'
  [08]: 0x62  // 'b'
  [09]: 0x6F  // 'o'
  [10]: 0x74  // 't'
```

### 2. BLE状态检查

确认：
- Central handle应该是0xFFFF（无效值）
- Peripheral handle可能是有效值（如果同时作为peripheral）
- Central mode应该是YES

### 3. 扫描停止结果

`Stopped scanning (result: 0x00)`中的result应该是0x00，如果不是，说明扫描停止失败。

## 常见问题和解决方案

### 问题1：设备名称仍显示乱码

**可能原因**：
- 目标设备广播数据格式异常
- 编码问题
- 广播数据截断

**解决方案**：
检查原始广播数据，手动解析设备名称字段

### 问题2：扫描停止失败

**现象**：`Stopped scanning (result: 0xXX)` 其中XX不是00

**解决方案**：
```cpp
// 在连接前强制停止扫描
sd_ble_gap_scan_stop();
nrf_delay_ms(100);
```

### 问题3：连接参数被拒绝

**现象**：第一次连接失败，第二次成功

**说明**：目标设备不接受您的连接参数，但接受默认参数

## 高级故障排除

### 如果所有方法都失败

1. **检查目标设备**：
   - 确认设备确实在广播
   - 确认设备接受连接
   - 尝试用手机连接该设备

2. **检查硬件**：
   - 天线连接
   - 供电稳定性
   - RF环境干扰

3. **检查软件配置**：
   - SoftDevice版本
   - SDK配置
   - 内存分配

### 临时解决方案：只发现不连接

如果连接问题持续，可以先实现发现功能：

```cpp
// 在scan_evt_handler中注释掉连接代码
// 只保留设备信息打印
NRF_LOG_INFO("Device discovered: %s", dev_name);
// 不尝试连接
```

## 预期结果

### 最佳情况
```
=== BLE State Check ===
Central handle: 0xFFFF
Peripheral handle: 0xFFFF
Central mode enabled: YES
=====================
Starting scanning for devices named: YRobot
Scanning started successfully
Raw adv data (31 bytes):
  [00]: 0x02
  [01]: 0x01
  [02]: 0x06
  [03]: 0x07
  [04]: 0x09
  [05]: 0x59  // 'Y'
  [06]: 0x52  // 'R'
  [07]: 0x6F  // 'o'
  [08]: 0x62  // 'b'
  [09]: 0x6F  // 'o'
  [10]: 0x74  // 't'
Found device: YRobot (name_found: 1)
MAC: CB:A8:54:52:CC:6C
RSSI: -45 dBm, Data len: 31
Stopped scanning (result: 0x00), attempting connection...
Connection params: min=80, max=160, latency=0, timeout=400
Connection request sent successfully
BLE Connected as Central
```

## 下一步

1. **立即测试**并记录完整的日志输出
2. **重点关注**：
   - 原始广播数据内容
   - 扫描停止结果
   - 连接尝试的详细过程
3. **如果仍有问题**，提供新的完整日志进行进一步分析

这次的修改应该能够解决大部分连接问题，或者至少提供足够的信息来确定根本原因。
