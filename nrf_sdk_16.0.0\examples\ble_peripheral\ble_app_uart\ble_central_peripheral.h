#ifndef BLE_CENTRAL_PERIPHERAL_H_
#define BLE_CENTRAL_PERIPHERAL_H_

#ifdef __cplusplus
extern "C" {
#endif

/**@brief Function to enable/disable central mode.
 *
 * @param[in] enable  True to enable central mode, false to disable.
 */
void setCentralMode(int enable);

/**@brief Function to get current central mode status.
 *
 * @return True if central mode is enabled, false otherwise.
 */
int getCentralMode(void);

/**@brief Function to send data via central connection.
 *
 * @param[in] buf  Pointer to data buffer.
 * @param[in] len  Length of data to send.
 *
 * @return NRF_SUCCESS on success, error code otherwise.
 */
unsigned int sendBluetoothMsgCentral(unsigned char *buf, unsigned short len);

/**@brief Function to check if central connection is active.
 *
 * @return True if central connection is active, false otherwise.
 */
int isCentralConnected(void);

/**@brief Function to check if peripheral connection is active.
 *
 * @return True if peripheral connection is active, false otherwise.
 */
int isPeripheralConnected(void);

/**@brief Function to send data via peripheral connection.
 *
 * @param[in] buf  Pointer to data buffer.
 * @param[in] len  Length of data to send.
 *
 * @return NRF_SUCCESS on success, error code otherwise.
 */
unsigned int sendBluetoothMsgPeripheral(unsigned char *buf, unsigned short len);

/**@brief Function to send protocol data to phone.
 *
 * @param[in] data_type  Type of data being sent.
 * @param[in] data       Pointer to data buffer.
 * @param[in] data_len   Length of data to send.
 *
 * @return NRF_SUCCESS on success, error code otherwise.
 */
unsigned int sendDataToPhone(uint8_t data_type, uint8_t *data, uint16_t data_len);

/**@brief Function to send protocol data to device.
 *
 * @param[in] data_type  Type of data being sent.
 * @param[in] data       Pointer to data buffer.
 * @param[in] data_len   Length of data to send.
 *
 * @return NRF_SUCCESS on success, error code otherwise.
 */
unsigned int sendDataToDevice(uint8_t data_type, uint8_t *data, uint16_t data_len);

/**@brief Function to send heartbeat to all connected devices.
 */
void sendHeartbeat(void);

/**@brief Function to send status information.
 */
void sendStatusInfo(void);

/**@brief Function to disconnect all connections gracefully.
 */
void disconnect_all_connections(void);

/**@brief Test function to demonstrate central/peripheral functionality.
 */
void testCentralPeripheralMode(void);

/**@brief Test function for data routing functionality.
 */
void testDataRouting(void);

/**@brief Function to demonstrate forwarding functionality.
 */
void testForwarding(void);

/**@brief Function to test NUS Client functionality.
 */
void testNUSClient(void);

/**@brief Function to set paired device serial number and start scanning.
 */
void setPairedDeviceAndStartScan(const char* device_serial_number);

/**@brief Function to clear paired device and stop scanning.
 */
void clearPairedDevice(void);

/**@brief Function to test paired device functionality.
 */
void testPairedDeviceFunction(void);

/**@brief Function to diagnose central connection status.
 */
void diagnoseCentralConnection(void);

#ifdef __cplusplus
}
#endif

#endif // BLE_CENTRAL_PERIPHERAL_H_
