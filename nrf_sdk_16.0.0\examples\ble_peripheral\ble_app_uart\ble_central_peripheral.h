#ifndef BLE_CENTRAL_PERIPHERAL_H_
#define BLE_CENTRAL_PERIPHERAL_H_

#ifdef __cplusplus
extern "C" {
#endif

/**@brief Function to enable/disable central mode.
 *
 * @param[in] enable  True to enable central mode, false to disable.
 */
void setCentralMode(int enable);

/**@brief Function to get current central mode status.
 *
 * @return True if central mode is enabled, false otherwise.
 */
int getCentralMode(void);

/**@brief Function to send data via central connection.
 *
 * @param[in] buf  Pointer to data buffer.
 * @param[in] len  Length of data to send.
 *
 * @return NRF_SUCCESS on success, error code otherwise.
 */
unsigned int sendBluetoothMsgCentral(unsigned char *buf, unsigned short len);

/**@brief Function to check if central connection is active.
 *
 * @return True if central connection is active, false otherwise.
 */
int isCentralConnected(void);

/**@brief Function to check if peripheral connection is active.
 *
 * @return True if peripheral connection is active, false otherwise.
 */
int isPeripheralConnected(void);

/**@brief Test function to demonstrate central/peripheral functionality.
 */
void testCentralPeripheralMode(void);

#ifdef __cplusplus
}
#endif

#endif // BLE_CENTRAL_PERIPHERAL_H_
