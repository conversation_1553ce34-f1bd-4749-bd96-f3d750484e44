<error> app: SOFTDEVICE: ASSERTION FAILED
<info> app: Setting vector table to bootloader: 0x00072000
<info> app: Setting vector table to main app: 0x00026000
<info> app_timer: RTC: initialized.
<info> app: Initializing fds...
<info> app: Event: FDS_EVT_INIT received (NRF_SUCCESS)
<info> app: Found 1 valid records.
<info> app: Found 0 dirty records (ready to be garbage collected).
<info> app: Config file found, updating boot count to 0.
<info> app: Config file found, main_board serial_number: [UNSET].
<info> app: Reading Serial Number from Flash.
<info> app: Config file found, main_board serial_number: [UNSET].
<info> app: Central mode ENABLED
<info> app: ble_stack_init
<info> app: Setting Device Name: [YRobot_C2:B7:5E:09:19:22]
<info> app: 

gatt_mtu_set
<info> app: ==== Current test configuration ====

<info> app: ATT MTU size:		247
Data length:		251
Connection interval:	12 units
Connection length ext:	on

<info> app: GAP event length:	800

<info> app: 
UART started.
<info> app: Debug logging for UART over RTT started.
<info> app: Initializing Central mode
<info> app: Scan initialized with filters for: YRobot_FE:8E:36:B5:36:D9
<info> app: === BLE State Check ===
<info> app: Central handle: 0xFFFF
<info> app: Peripheral handle: 0xFFFF
<info> app: Central mode enabled: YES
<info> app: =====================
<info> app: Starting scanning for devices named: YRobot_FE:8E:36:B5:36:D9
<info> app: Scanning started successfully
<info> app: Starting Peripheral mode (advertising)
<info> app: Init SPIS
<info> app: Software version: 0.1.0
<info> app: Found device: YRobot_FE:8E:36:B5:36:D9 
<info> app: MAC: FE:8E:36:B5:36:D9
<info> app: RSSI: -60 dBm, Data len: 29
<info> app: Adv type: 0x01, Connectable: YES
<info> app: Stopped scanning (result: 0x0), attempting connection...
<info> app: Connection params: min=12, max=12, latency=0, timeout=400
<info> app: Original addr type: 1
<info> app: Attempting connection with custom parameters...
<info> app: Connection request sent successfully
<info> app: BLE Connected as Central
<info> app: [App::handleConnected] - clear BLE Tx Buffer [0]
<info> app: Sending connection state to target.
<info> app: 	BLE connected changed [0] -> [1]
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<info> app: [2679] [A77] ATT MTU exchange completed. central 0xF7 peripheral 0xF7
<info> app: [2679] ATT MTU exchange completed. MTU set to 247 bytes.
<info> app: [55930] [DA7A] ATT MTU exchange completed. central 0xF7 peripheral 0xF7
<info> app: [55930] Data length updated to 251 bytes.






<info> app: [00:00:29] BLE Rx [00] Tx [00 | 54 | 00]	SPI Rx [195] Tx [195]
<info> app: [00:00:30] BLE Rx [00] Tx [00 | 54 | 00]	SPI Rx [194] Tx [194]
<info> app: [00:00:31] BLE Rx [00] Tx [00 | 55 | 00]	SPI Rx [194] Tx [194]
<info> app: [00:00:32] BLE Rx [00] Tx [00 | 54 | 00]	SPI Rx [195] Tx [195]
<info> app: [00:00:33] BLE Rx [00] Tx [00 | 54 | 00]	SPI Rx [194] Tx [194]
<info> app: [00:00:34] BLE Rx [00] Tx [00 | 55 | 00]	SPI Rx [194] Tx [194]
<info> app: BLE Connected as Peripheral
<info> app: [App::handleConnected] - clear BLE Tx Buffer [0]
<info> app: Sending connection state to target.
<info> app: 	BLE connected changed [0] -> [1]
<info> app: [55930] [DA7A] ATT MTU exchange completed. central 0xF7 peripheral 0xF7
<info> app: [55930] Data length updated to 251 bytes.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<info> app: [00:00:35] BLE Rx [00] Tx [00 | 54 | 00]	SPI Rx [194] Tx [194]
<info> app: Data len is set to 0xF4(244)
<info> app: [2679] [A77] ATT MTU exchange completed. central 0xF7 peripheral 0xF7
<info> app: [2679] ATT MTU exchange completed. MTU set to 247 bytes.
<info> app: [00:00:36] BLE Rx [00] Tx [00 | 54 | 00]	SPI Rx [194] Tx [194]
<info> app: [00:00:37] BLE Rx [00] Tx [00 | 55 | 00]	SPI Rx [195] Tx [195]
<info> app: [00:00:38] BLE Rx [00] Tx [00 | 54 | 00]	SPI Rx [194] Tx [194]
<info> app: [00:00:39] BLE Rx [00] Tx [00 | 54 | 00]	SPI Rx [194] Tx [194]
<info> app: [00:00:40] BLE Rx [00] Tx [00 | 55 | 00]	SPI Rx [195] Tx [195]
<info> ble_nus: 	p_client notification enabled [1]
<info> app: Connection delay [6229 ms]
<info> app: [App::sendBluetoothMsgDirectLen() BLE Tx Ready] - clear BLE Tx Buffer [1]
<info> app: 	BLE TX Ready changed [0] -> [1]
<info> app: [00:00:41] BLE Rx [01] Tx [57 | 55 | 00]	SPI Rx [194] Tx [194]
<info> app: [00:00:42] BLE Rx [00] Tx [54 | 54 | 00]	SPI Rx [194] Tx [194]



<info> app: Setting vector table to bootloader: 0x00072000
<info> app: Setting vector table to main app: 0x00026000
<info> app_timer: RTC: initialized.
<info> app: Initializing fds...
<info> app: Event: FDS_EVT_INIT received (NRF_SUCCESS)
<info> app: Found 1 valid records.
<info> app: Found 0 dirty records (ready to be garbage collected).
<info> app: Config file found, updating boot count to 0.
<info> app: Config file found, main_board serial_number: [UNSET].
<info> app: Reading Serial Number from Flash.
<info> app: Config file found, main_board serial_number: [UNSET].
<info> app: Reading Paired Device Serial Number from Flash.
<info> app: Config file found, paired device serial_number: [FE:8E:36:B5:36:D9].
<info> app: Central mode ENABLED
<info> app: ble_stack_init
<info> app: Setting Device Name: [YRobot_C2:B7:5E:09:19:22]
<info> app: Initializing Database Discovery...
<info> app: Database Discovery initialized successfully
<info> app: Initializing NUS Client...
<info> app: NUS Client initialized successfully
<info> app: 

gatt_mtu_set
<info> app: ==== Current test configuration ====

<info> app: ATT MTU size:		247
Data length:		251
Connection interval:	12 units
Connection length ext:	on

<info> app: GAP event length:	800

<info> app: 
UART started.
<info> app: Debug logging for UART over RTT started.
<info> app: Initializing Central mode
<info> app: Found valid paired device serial number: [FE:8E:36:B5:36:D9]
<info> app: Updated target device name to: [YRobot_FE:8E:36:B5:36:D9]
<info> app: Scan initialized with filters for: YRobot_FE:8E:36:B5:36:D9
<info> app: === BLE State Check ===
<info> app: Central handle: 0xFFFF
<info> app: Peripheral handle: 0xFFFF
<info> app: Central mode enabled: YES
<info> app: =====================
<info> app: Starting scanning for devices named: YRobot_FE:8E:36:B5:36:D9
<info> app: Scanning started successfully
<info> app: Starting Peripheral mode (advertising)
<info> app: Init SPIS
<info> app: Software version: 0.1.0
<info> app: Found device: YRobot_FE:8E:36:B5:36:D9 
<info> app: MAC: FE:8E:36:B5:36:D9
<info> app: RSSI: -49 dBm, Data len: 29
<info> app: Adv type: 0x01, Connectable: YES
<info> app: Stopped scanning (result: 0x0), attempting connection...
<info> app: Connection params: min=12, max=12, latency=0, timeout=400
<info> app: Original addr type: 1
<info> app: Attempting connection with custom parameters...
<info> app: Connection request sent successfully
<info> app: BLE Connected as Central
<info> app: Starting GATT service discovery...
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<info> app: [2679] [A77] ATT MTU exchange completed. central 0xF7 peripheral 0xF7
<info> app: [2679] ATT MTU exchange completed. MTU set to 247 bytes.
<info> app: [55930] [DA7A] ATT MTU exchange completed. central 0xF7 peripheral 0xF7
<info> app: [55930] Data length updated to 251 bytes.
<info> app: PHY update request.
<info> app: [00:00:01] BLE Rx [00] Tx [00 | 00 | 00]	SPI Rx [195] Tx [196]
<info> app: Database discovery event: type=0, conn_handle=0x0000
<info> app: Database discovery complete for connection 0x0000
<info> app: NUS service discovered on the server.
<info> app: NUS RX handle: 0x000D
<info> app: NUS TX handle: 0x000F
<info> app: NUS TX CCCD handle: 0x0010
<info> app: Connection handle: 0x0000
<info> app: Stored TX CCCD handle: 0x0010
<info> app: Enabling TX notifications...
<info> app: TX notifications enabled successfully
<info> app: Connected to device with Nordic UART Service.
<info> app: Database discovery event: type=3, conn_handle=0x0000
<info> app: Database discovery available for connection 0x0000
<info> app: Received data from NUS TX characteristic.
<info> app:  64 22 95 08 C1 DF 01 10|d"......
<info> app:  98 15 52 19 08 01 18 16|..R.....
<info> app:  22 13 4E 4F 54 5F 49 4D|".NOT_IM
<info> app:  50 4C 45 4D 45 4E 54 45|PLEMENTE
<info> app:  44 5F 59 45 54         |D_YET   
<info> app: Processing data from device, length: 37
<info> app: Received data from NUS TX characteristic.
<info> app:  64 22 62 08 CB DF 01 10|d"b.....
<info> app:  99 15 52 19 08 01 18 16|..R.....
<info> app:  22 13 4E 4F 54 5F 49 4D|".NOT_IM
<info> app:  50 4C 45 4D 45 4E 54 45|PLEMENTE
<info> app:  44 5F 59 45 54         |D_YET   
<info> app: Processing data from device, length: 37