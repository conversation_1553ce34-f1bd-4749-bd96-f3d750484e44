<error> app: SOFTDEVICE: ASSERTION FAILED
<info> app: Setting vector table to bootloader: 0x00072000
<info> app: Setting vector table to main app: 0x00026000
<info> app_timer: RTC: initialized.
<info> app: Initializing fds...
<info> app: Event: FDS_EVT_INIT received (NRF_SUCCESS)
<info> app: Found 1 valid records.
<info> app: Found 0 dirty records (ready to be garbage collected).
<info> app: Config file found, updating boot count to 0.
<info> app: Config file found, main_board serial_number: [UNSET].
<info> app: Reading Serial Number from Flash.
<info> app: Config file found, main_board serial_number: [UNSET].
<info> app: Central mode ENABLED
<info> app: ble_stack_init
<info> app: Setting Device Name: [YRobot_C2:B7:5E:09:19:22]
<info> app: 

gatt_mtu_set
<info> app: ==== Current test configuration ====

<info> app: ATT MTU size:		247
Data length:		251
Connection interval:	12 units
Connection length ext:	on

<info> app: GAP event length:	800

<info> app: 
UART started.
<info> app: Debug logging for UART over RTT started.
<info> app: Initializing Central mode
<info> app: Scan initialized with filters for: YRobot_FE:8E:36:B5:36:D9
<info> app: === BLE State Check ===
<info> app: Central handle: 0xFFFF
<info> app: Peripheral handle: 0xFFFF
<info> app: Central mode enabled: YES
<info> app: =====================
<info> app: Starting scanning for devices named: YRobot_FE:8E:36:B5:36:D9
<info> app: Scanning started successfully
<info> app: Starting Peripheral mode (advertising)
<info> app: Init SPIS
<info> app: Software version: 0.1.0
<info> app: Found device: YRobot_FE:8E:36:B5:36:D9 
<info> app: MAC: FE:8E:36:B5:36:D9
<info> app: RSSI: -60 dBm, Data len: 29
<info> app: Adv type: 0x01, Connectable: YES
<info> app: Stopped scanning (result: 0x0), attempting connection...
<info> app: Connection params: min=12, max=12, latency=0, timeout=400
<info> app: Original addr type: 1
<info> app: Attempting connection with custom parameters...
<info> app: Connection request sent successfully
<info> app: BLE Connected as Central
<info> app: [App::handleConnected] - clear BLE Tx Buffer [0]
<info> app: Sending connection state to target.
<info> app: 	BLE connected changed [0] -> [1]
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<error> nrf_ble_gatt: sd_ble_gatts_exchange_mtu_reply() returned NRF_ERROR_INVALID_STATE.
<info> app: [2679] [A77] ATT MTU exchange completed. central 0xF7 peripheral 0xF7
<info> app: [2679] ATT MTU exchange completed. MTU set to 247 bytes.
<info> app: [55930] [DA7A] ATT MTU exchange completed. central 0xF7 peripheral 0xF7
<info> app: [55930] Data length updated to 251 bytes.