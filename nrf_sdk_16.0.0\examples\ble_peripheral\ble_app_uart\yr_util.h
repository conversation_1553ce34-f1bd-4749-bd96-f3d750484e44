#ifndef YR_UTIL_H_
#define YR_UTIL_H_

// #define USE_PEER_MANAGER
// #define USE_UART

#define USE_NRF_DFU
#define USE_RINGBUF
#define USE_IOS_CONN_INTERVAL

#include "util/common.h"
#include "util/RingBufCPP.h"

#ifdef __cplusplus
extern "C" {
#endif

// clang-format off

#ifdef USE_NRF_DFU
#include "nrf_dfu_ble_svci_bond_sharing.h"
#include "nrf_svci_async_function.h"
#include "nrf_svci_async_handler.h"
#endif // USE_NRF_DFU

// #define SEC_PARAM_BOND 0                               /**< Perform bonding. */
#define SEC_PARAM_BOND 1                               /**< Perform bonding. */
#define SEC_PARAM_MITM 0                               /**< Man In The Middle protection not required. */
#define SEC_PARAM_LESC 0                               /**< LE Secure Connections not enabled. */
#define SEC_PARAM_KEYPRESS 0                           /**< Keypress notifications not enabled. */
#define SEC_PARAM_IO_CAPABILITIES BLE_GAP_IO_CAPS_NONE /**< No I/O capabilities. */
#define SEC_PARAM_OOB 0                                /**< Out Of Band data not available. */
#define SEC_PARAM_MIN_KEY_SIZE 7                       /**< Minimum encryption key size. */
#define SEC_PARAM_MAX_KEY_SIZE 16                      /**< Maximum encryption key size. */

// #define SEC_PARAM_LESC 1

#include <stdint.h>
#include <string.h>
#include "nordic_common.h"
#include "nrf.h"
#include "ble_hci.h"
#include "ble_dis.h"
#include "ble_err.h"
#include "ble_advertising.h"
#include "ble_advdata.h"
#include "ble_conn_params.h"
#include "nrf_sdh.h"
#include "nrf_sdh_soc.h"
#include "nrf_sdh_ble.h"
#include "nrf_ble_gatt.h"
#include "nrf_ble_qwr.h"
#include "nrf_ble_lesc.h"
#include "app_timer.h"
#include "ble_nus.h"
#include "app_uart.h"
#include "app_util_platform.h"
#include "bsp_btn_ble.h"
#include "nrf_pwr_mgmt.h"
#include "nrf_ble_scan.h"

#include "fds.h"
#include "ble_conn_state.h"
#include "app_error.h"
#include "nrf_sdm.h"
#include "ble_srv_common.h"
#include "bsp_btn_ble.h"

#include "peer_manager.h"
#include "peer_manager_handler.h"

// #include "ble_bas.h"
// #include "ble_hrs.h"

#if defined (UART_PRESENT)
#include "nrf_uart.h"
#endif
#if defined (UARTE_PRESENT)
#include "nrf_uarte.h"
#endif

#include "nrf_drv_spis.h"

#ifdef USE_NRF_DFU
#include "nrf_pwr_mgmt.h"
#include "peer_manager.h"
#include "peer_manager_handler.h"

#include "ble_conn_state.h"
#include "ble_dfu.h"
#include "fds.h"
#include "nrf_drv_clock.h"
#include "nrf_power.h"
#include "nrf_bootloader_info.h"

// #define SEC_PARAM_BOND 0                               /**< Perform bonding. */
// #define SEC_PARAM_MITM 0                               /**< Man In The Middle protection not required. */
// // #define SEC_PARAM_LESC 0                               /**< LE Secure Connections not enabled. */
// // #define SEC_PARAM_LESC 1                               /**< LE Secure Connections not enabled. */
// #define SEC_PARAM_KEYPRESS 0                           /**< Keypress notifications not enabled. */
// #define SEC_PARAM_IO_CAPABILITIES BLE_GAP_IO_CAPS_NONE /**< No I/O capabilities. */
// #define SEC_PARAM_OOB 0                                /**< Out Of Band data not available. */
// #define SEC_PARAM_MIN_KEY_SIZE 7                       /**< Minimum encryption key size. */
// #define SEC_PARAM_MAX_KEY_SIZE 16                      /**< Maximum encryption key size. */
#endif // USE_NRF_DFU

#include "nrf_log.h"
#include "nrf_log_ctrl.h"
#include "nrf_log_default_backends.h"

#define APP_BLE_CONN_CFG_TAG            1                                           /**< A tag identifying the SoftDevice BLE configuration. */

#define DEVICE_NAME                     "YRobot"                                    /**< Name of device. Will be included in the advertising data. */
#define NUS_SERVICE_UUID_TYPE           BLE_UUID_TYPE_VENDOR_BEGIN                  /**< UUID type for the Nordic UART Service (vendor specific). */

#define APP_BLE_OBSERVER_PRIO           3                                           /**< Application's BLE observer priority. You shouldn't need to modify this value. */
#define APP_ADV_INTERVAL                256                                         /**< The advertising interval (in units of 0.625 ms. This value corresponds to 40 ms). */
#define APP_ADV_DURATION                (100*180)                                   /**< The advertising duration (180 seconds) in units of 10 milliseconds. */
// #define APP_ADV_DURATION                0                                        /**< The advertising duration (180 seconds) in units of 10 milliseconds. */

#define MIN_CONN_INTERVAL               MSEC_TO_UNITS(15, UNIT_1_25_MS)
#define MAX_CONN_INTERVAL               MSEC_TO_UNITS(30, UNIT_1_25_MS)

#ifdef USE_IOS_CONN_INTERVAL
#define CONN_INTERVAL_DEFAULT           (uint16_t)(MSEC_TO_UNITS(15, UNIT_1_25_MS)) /**< Default connection interval used at connection establishment by central side. */
#define MIN_CONN_INTERVAL               MSEC_TO_UNITS(15, UNIT_1_25_MS)             /**< Minimum acceptable connection interval (20 ms), Connection interval uses 1.25 ms units. */
#define MAX_CONN_INTERVAL               MSEC_TO_UNITS(30, UNIT_1_25_MS)             /**< Maximum acceptable connection interval (75 ms), Connection interval uses 1.25 ms units. */
#else
#define CONN_INTERVAL_DEFAULT           (uint16_t)(MSEC_TO_UNITS(7.5, UNIT_1_25_MS)) /**< Default connection interval used at connection establishment by central side. */
#define MIN_CONN_INTERVAL               MSEC_TO_UNITS(7.5, UNIT_1_25_MS)             /**< Minimum acceptable connection interval (20 ms), Connection interval uses 1.25 ms units. */
#define MAX_CONN_INTERVAL               MSEC_TO_UNITS(15, UNIT_1_25_MS)             /**< Maximum acceptable connection interval (75 ms), Connection interval uses 1.25 ms units. */
#endif // USE_IOS_CONN_INTERVAL

#define SLAVE_LATENCY                   0                                           /**< Slave latency. */
#define CONN_SUP_TIMEOUT                MSEC_TO_UNITS(4000, UNIT_10_MS)             /**< Connection supervisory timeout (4 seconds), Supervision Timeout uses 10 ms units. */
#define FIRST_CONN_PARAMS_UPDATE_DELAY  APP_TIMER_TICKS(5000)                       /**< Time from initiating event (connect or start of notification) to first time sd_ble_gap_conn_param_update is called (5 seconds). */
// #define FIRST_CONN_PARAMS_UPDATE_DELAY  APP_TIMER_TICKS(100)                       /**< Time from initiating event (connect or start of notification) to first time sd_ble_gap_conn_param_update is called (5 seconds). */
#define NEXT_CONN_PARAMS_UPDATE_DELAY   APP_TIMER_TICKS(30000)                      /**< Time between each call to sd_ble_gap_conn_param_update after the first call (30 seconds). */
#define MAX_CONN_PARAMS_UPDATE_COUNT    3                                           /**< Number of attempts before giving up the connection parameter negotiation. */

#define DEAD_BEEF                       0xDEADBEEF                                  /**< Value used as error code on stack dump, can be used to identify stack location on stack unwind. */

#define UART_TX_BUF_SIZE                256                                         /**< UART TX buffer size. */
#define UART_RX_BUF_SIZE                256                                         /**< UART RX buffer size. */

BLE_NUS_DEF(m_nus, NRF_SDH_BLE_TOTAL_LINK_COUNT);                                   /**< BLE NUS service instance. */
NRF_BLE_GATT_DEF(m_gatt);                                                           /**< GATT module instance. */
NRF_BLE_QWR_DEF(m_qwr);                                                             /**< Context for the Queued Write module.*/
BLE_ADVERTISING_DEF(m_advertising);                                                 /**< Advertising module instance. */

// static uint16_t   m_conn_handle          = BLE_CONN_HANDLE_INVALID;                 /**< Handle of the current connection. */
extern uint16_t   m_conn_handle;
static uint16_t   m_ble_nus_max_data_len = BLE_GATT_ATT_MTU_DEFAULT - 3;            /**< Maximum length of data (in bytes) that can be transmitted to the peer by the Nordic UART service module. */
static ble_uuid_t m_adv_uuids[]          =                                          /**< Universally unique service identifier. */
{
    {BLE_UUID_NUS_SERVICE, NUS_SERVICE_UUID_TYPE}
#ifdef USE_NRF_DFU
,  { BLE_UUID_DEVICE_INFORMATION_SERVICE, BLE_UUID_TYPE_BLE }
#endif // USE_NRF_DFU
};
// clang-format on

#define YR_MICROS_INTERVAL APP_TIMER_TICKS(1)
APP_TIMER_DEF(sd_timer_id);
APP_TIMER_DEF(main_loop_timer_id);

void timer_main_loop_handler(void *p_context);

#define MEM_ADDR_FACTORY_PROGRAM ((uint32_t)0x3A000)
#define MEM_ADDR_NEW_PROGRAM ((uint32_t)0x9A000)
#define MEM_ADDR_PROGRAM_SELECT ((uint32_t)0x99000)
#define MEM_ADDR_PROGRAM_CRC_SIZE ((uint32_t)MEM_ADDR_PROGRAM_SELECT + 0x10)
#define MEM_ADDR_PROGRAM_CRC_VALUE ((uint32_t)MEM_ADDR_PROGRAM_SELECT + 0x20)

#define SPIS_INSTANCE 1                                                  /**< SPIS instance index. */
static const nrf_drv_spis_t spis = NRF_DRV_SPIS_INSTANCE(SPIS_INSTANCE); /**< SPIS instance. */

extern uint8_t m_adv_handle; /**< Advertising handle used to identify an advertising set. */

//----------------------------------------------------------------------------//
//  ATT Max Throughput
//----------------------------------------------------------------------------//
typedef struct {
  uint16_t att_mtu;              /**< GATT ATT MTU, in bytes. */
  uint16_t conn_interval;        /**< Connection interval expressed in units of 1.25 ms. */
  ble_gap_phys_t phys;           /**< Preferred PHYs. */
  uint8_t data_len;              /**< Data length. */
  bool conn_evt_len_ext_enabled; /**< Connection event length extension status. */
} test_params_t;
// Test parameters.
// Settings like ATT MTU size are set only once, on the dummy board.
// Make sure that defaults are sensible.
static test_params_t m_test_params = {
  // .att_mtu = NRF_SDH_BLE_GATT_MAX_MTU_SIZE,
  // .conn_interval = CONN_INTERVAL_DEFAULT,
  // .phys.tx_phys = BLE_GAP_PHY_2MBPS | BLE_GAP_PHY_1MBPS,
  // .phys.rx_phys = BLE_GAP_PHY_2MBPS | BLE_GAP_PHY_1MBPS,
  // .data_len = NRF_SDH_BLE_GAP_DATA_LENGTH,
  // .conn_evt_len_ext_enabled = true
  NRF_SDH_BLE_GATT_MAX_MTU_SIZE,
  CONN_INTERVAL_DEFAULT,
  BLE_GAP_PHY_2MBPS | BLE_GAP_PHY_1MBPS,
  BLE_GAP_PHY_2MBPS | BLE_GAP_PHY_1MBPS,
  NRF_SDH_BLE_GAP_DATA_LENGTH,
  true
  // Only symmetric PHYs are supported.
  // #if defined(S140)
  //   .phys.tx_phys = BLE_GAP_PHY_2MBPS | BLE_GAP_PHY_1MBPS | BLE_GAP_PHY_CODED,
  //   .phys.rx_phys = BLE_GAP_PHY_2MBPS | BLE_GAP_PHY_1MBPS | BLE_GAP_PHY_CODED,
  // #else
  // #endif
};


// Connection parameters requested for connection.
// static ble_gap_conn_params_t m_conn_param = {
//   // .min_conn_interval = CONN_INTERVAL_MIN,  // Minimum connection interval.
//   // .max_conn_interval = CONN_INTERVAL_MAX,  // Maximum connection interval.
//   // .slave_latency = SLAVE_LATENCY,          // Slave latency.
//   // .conn_sup_timeout = CONN_SUP_TIMEOUT     // Supervisory timeout.
//   MIN_CONN_INTERVAL,  // Minimum connection interval.
//   MAX_CONN_INTERVAL,  // Maximum connection interval.
//   SLAVE_LATENCY,      // Slave latency.
//   CONN_SUP_TIMEOUT    // Supervisory timeout.
// };

static void gatt_mtu_set(uint16_t att_mtu) {
  ret_code_t err_code;

  m_test_params.att_mtu = att_mtu;

  NRF_LOG_INFO("\r\n\r\ngatt_mtu_set");

  err_code = nrf_ble_gatt_att_mtu_periph_set(&m_gatt, att_mtu);
  APP_ERROR_CHECK(err_code);

  err_code = nrf_ble_gatt_att_mtu_central_set(&m_gatt, att_mtu);
  APP_ERROR_CHECK(err_code);
}


static void connection_interval_set(uint16_t value) {
  m_test_params.conn_interval = value;
}


static void conn_evt_len_ext_set(bool status) {
  ret_code_t err_code;
  ble_opt_t opt;

  memset(&opt, 0x00, sizeof(opt));
  opt.common_opt.conn_evt_ext.enable = status ? 1 : 0;

  err_code = sd_ble_opt_set(BLE_COMMON_OPT_CONN_EVT_EXT, &opt);
  APP_ERROR_CHECK(err_code);

  m_test_params.conn_evt_len_ext_enabled = status;
}


static void data_len_set(uint8_t value) {
  ret_code_t err_code;
  err_code = nrf_ble_gatt_data_length_set(&m_gatt, BLE_CONN_HANDLE_INVALID, value);
  APP_ERROR_CHECK(err_code);

  m_test_params.data_len = value;
}


static void print_ble_config() {
  NRF_LOG_INFO("==== Current test configuration ====\r\n");
  NRF_LOG_INFO(
      "ATT MTU size:\t\t%d\r\n"
      "Data length:\t\t%d\r\n"
      "Connection interval:\t%d units\r\n"
      "Connection length ext:\t%s\r\n",
      // "Preferred PHY:\t\t%s\r\n",
      m_test_params.att_mtu, m_test_params.data_len, m_test_params.conn_interval,
      m_test_params.conn_evt_len_ext_enabled ? "on" : "off");
  // , phy_str(m_test_params.phys));
  NRF_LOG_INFO("GAP event length:\t%d\r\n", NRF_SDH_BLE_GAP_EVENT_LENGTH);
}

//----------------------------------------------------------------------------//
//  ATT Max Throughput
//----------------------------------------------------------------------------//
#ifdef USE_NRF_DFU
static void disconnect(uint16_t conn_handle, void *p_context) {
  UNUSED_PARAMETER(p_context);

  ret_code_t err_code = sd_ble_gap_disconnect(conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
  if (err_code != NRF_SUCCESS) {
    NRF_LOG_WARNING("Failed to disconnect connection. Connection handle: %d Error: %d", conn_handle, err_code);
  } else {
    NRF_LOG_DEBUG("Disconnected connection handle %d", conn_handle);
  }
}

/**@brief Handler for shutdown preparation.
 *
 * @details During shutdown procedures, this function will be called at a 1 second interval
 *          untill the function returns true. When the function returns true, it means that the
 *          app is ready to reset to DFU mode.
 *
 * @param[in]   event   Power manager event.
 *
 * @retval  True if shutdown is allowed by this power manager handler, otherwise false.
 */
static bool app_shutdown_handler(nrf_pwr_mgmt_evt_t event) {
  switch (event) {
    case NRF_PWR_MGMT_EVT_PREPARE_DFU:
      NRF_LOG_INFO("Power management wants to reset to DFU mode.");
      // YOUR_JOB: Get ready to reset into DFU mode
      //
      // If you aren't finished with any ongoing tasks, return "false" to
      // signal to the system that reset is impossible at this stage.
      //
      // Here is an example using a variable to delay resetting the device.
      //
      // if (!m_ready_for_reset)
      // {
      //      return false;
      // }
      // else
      //{
      //
      //    // Device ready to enter
      //    uint32_t err_code;
      //    err_code = sd_softdevice_disable();
      //    APP_ERROR_CHECK(err_code);
      //    err_code = app_timer_stop_all();
      //    APP_ERROR_CHECK(err_code);
      //}
      break;

    default:
      // YOUR_JOB: Implement any of the other events available from the power management module:
      //      -NRF_PWR_MGMT_EVT_PREPARE_SYSOFF
      //      -NRF_PWR_MGMT_EVT_PREPARE_WAKEUP
      //      -NRF_PWR_MGMT_EVT_PREPARE_RESET
      return true;
  }

  NRF_LOG_INFO("Power management allowed to reset to DFU mode.");
  return true;
}


// lint -esym(528, m_app_shutdown_handler)
/**@brief Register application shutdown handler with priority 0.
 */
NRF_PWR_MGMT_HANDLER_REGISTER(app_shutdown_handler, 0);


static void buttonless_dfu_sdh_state_observer(nrf_sdh_state_evt_t state, void *p_context) {
  if (state == NRF_SDH_EVT_STATE_DISABLED) {
    // Softdevice was disabled before going into reset. Inform bootloader to skip CRC on next boot.
    nrf_power_gpregret2_set(BOOTLOADER_DFU_SKIP_CRC);

    // Go to system off.
    nrf_pwr_mgmt_shutdown(NRF_PWR_MGMT_SHUTDOWN_GOTO_SYSOFF);
  }
}

/* nrf_sdh state observer. */
NRF_SDH_STATE_OBSERVER(m_buttonless_dfu_state_obs, 0) = {
  .handler = buttonless_dfu_sdh_state_observer,
};


static void advertising_config_get(ble_adv_modes_config_t *p_config) {
  memset(p_config, 0, sizeof(ble_adv_modes_config_t));

  p_config->ble_adv_fast_enabled = true;
  p_config->ble_adv_fast_interval = APP_ADV_INTERVAL;
  p_config->ble_adv_fast_timeout = APP_ADV_DURATION;
}

// YOUR_JOB: Update this code if you want to do anything given a DFU event (optional).
/**@brief Function for handling dfu events from the Buttonless Secure DFU service
 *
 * @param[in]   event   Event from the Buttonless Secure DFU service.
 */
static void ble_dfu_evt_handler(ble_dfu_buttonless_evt_type_t event) {
  switch (event) {
    case BLE_DFU_EVT_BOOTLOADER_ENTER_PREPARE: {
      NRF_LOG_INFO("Device is preparing to enter bootloader mode.");

      // Prevent device from advertising on disconnect.
      ble_adv_modes_config_t config;
      advertising_config_get(&config);
      config.ble_adv_on_disconnect_disabled = true;
      ble_advertising_modes_config_set(&m_advertising, &config);

      // Disconnect all other bonded devices that currently are connected.
      // This is required to receive a service changed indication
      // on bootup after a successful (or aborted) Device Firmware Update.
      uint32_t conn_count = ble_conn_state_for_each_connected(disconnect, NULL);
      NRF_LOG_INFO("Disconnected %d links.", conn_count);
      break;
    }

    case BLE_DFU_EVT_BOOTLOADER_ENTER:
      // YOUR_JOB: Write app-specific unwritten data to FLASH, control finalization of this
      //           by delaying reset by reporting false in app_shutdown_handler
      NRF_LOG_INFO("Device will enter bootloader mode.");
      break;

    case BLE_DFU_EVT_BOOTLOADER_ENTER_FAILED:
      NRF_LOG_ERROR("Request to enter bootloader mode failed asynchroneously.");
      // YOUR_JOB: Take corrective measures to resolve the issue
      //           like calling APP_ERROR_CHECK to reset the device.
      break;

    case BLE_DFU_EVT_RESPONSE_SEND_ERROR:
      NRF_LOG_ERROR("Request to send a response to client failed.");
      // YOUR_JOB: Take corrective measures to resolve the issue
      //           like calling APP_ERROR_CHECK to reset the device.
      APP_ERROR_CHECK(false);
      break;

    default:
      NRF_LOG_ERROR("Unknown event from ble_dfu_buttonless.");
      break;
  }
}

// #if 0
// /**@brief Function for handling Peer Manager events.
//  *
//  * @param[in] p_evt  Peer Manager event.
//  */
// static void pm_evt_handler(pm_evt_t const *p_evt) {
//   pm_handler_on_pm_evt(p_evt);
//   pm_handler_flash_clean(p_evt);
// }
//
//
// /**@brief Function for the Peer Manager initialization.
//  */
// static void peer_manager_init() {
//   ble_gap_sec_params_t sec_param;
//   ret_code_t err_code;
//
//   err_code = pm_init();
//   APP_ERROR_CHECK(err_code);
//
//   memset(&sec_param, 0, sizeof(ble_gap_sec_params_t));
//
//   // Security parameters to be used for all security procedures.
//   sec_param.bond = SEC_PARAM_BOND;
//   sec_param.mitm = SEC_PARAM_MITM;
//   sec_param.lesc = SEC_PARAM_LESC;
//   sec_param.keypress = SEC_PARAM_KEYPRESS;
//   sec_param.io_caps = SEC_PARAM_IO_CAPABILITIES;
//   sec_param.oob = SEC_PARAM_OOB;
//   sec_param.min_key_size = SEC_PARAM_MIN_KEY_SIZE;
//   sec_param.max_key_size = SEC_PARAM_MAX_KEY_SIZE;
//   sec_param.kdist_own.enc = 1;
//   sec_param.kdist_own.id = 1;
//   sec_param.kdist_peer.enc = 1;
//   sec_param.kdist_peer.id = 1;
//
//   err_code = pm_sec_params_set(&sec_param);
//   APP_ERROR_CHECK(err_code);
//
//   err_code = pm_register(pm_evt_handler);
//   APP_ERROR_CHECK(err_code);
// }
// #endif

#endif  // USE_NRF_DFU

/**@brief Function for starting advertising.
 */
// static void advertising_start(void) {
//   uint32_t err_code = ble_advertising_start(&m_advertising, BLE_ADV_MODE_FAST);
//   APP_ERROR_CHECK(err_code);
// }

/**@brief Clear bond information from persistent storage.
 */
static void delete_bonds(void) {
  ret_code_t err_code;

  NRF_LOG_INFO("Erase bonds!");

  err_code = pm_peers_delete();
  APP_ERROR_CHECK(err_code);
}

static void advertising_start(bool erase_bonds) {
  if (erase_bonds == true) {
    delete_bonds();
    // Advertising is started by PM_EVT_PEERS_DELETE_SUCCEEDED event.
  } else {
    ret_code_t err_code;

    err_code = ble_advertising_start(&m_advertising, BLE_ADV_MODE_FAST);
    APP_ERROR_CHECK(err_code);
  }
}

/**@brief Function for handling Peer Manager events.
 *
 * @param[in] p_evt  Peer Manager event.
 */
static void pm_evt_handler(pm_evt_t const *p_evt) {
  pm_handler_on_pm_evt(p_evt);
  pm_handler_flash_clean(p_evt);

  switch (p_evt->evt_id) {
    case PM_EVT_PEERS_DELETE_SUCCEEDED:
      advertising_start(false);
      break;
    default:
      break;
  }
}

/**@brief Function for the Peer Manager initialization.
 */
static void peer_manager_init() {
  ble_gap_sec_params_t sec_param;
  ret_code_t err_code;

  err_code = pm_init();
  APP_ERROR_CHECK(err_code);

  memset(&sec_param, 0, sizeof(ble_gap_sec_params_t));

  // Security parameters to be used for all security procedures.
  sec_param.bond = SEC_PARAM_BOND;
  sec_param.mitm = SEC_PARAM_MITM;
  sec_param.lesc = SEC_PARAM_LESC;
  sec_param.keypress = SEC_PARAM_KEYPRESS;
  sec_param.io_caps = SEC_PARAM_IO_CAPABILITIES;
  sec_param.oob = SEC_PARAM_OOB;
  sec_param.min_key_size = SEC_PARAM_MIN_KEY_SIZE;
  sec_param.max_key_size = SEC_PARAM_MAX_KEY_SIZE;
  sec_param.kdist_own.enc = 1;
  sec_param.kdist_own.id = 1;
  sec_param.kdist_peer.enc = 1;
  sec_param.kdist_peer.id = 1;

  err_code = pm_sec_params_set(&sec_param);
  APP_ERROR_CHECK(err_code);

  err_code = pm_register(pm_evt_handler);
  APP_ERROR_CHECK(err_code);
}

#ifdef __cplusplus
}
#endif

static uint32_t g_time_connected = 0;

static uint32_t g_send_bluetooth(uint8_t *buf, uint16_t length_in) {
  // NRF_LOG_INFO("g_send_ble [%d] [%d]", &m_nus, m_conn_handle);
  uint16_t length = length_in;
  return ble_nus_data_send(&m_nus, buf, &length, m_conn_handle);
}

// uint32_t g_send_bluetooth(uint8_t *buf, const uint16_t &length_in);


//----------------------------------------------------------------------------//
//
//----------------------------------------------------------------------------//
void nus_data_handler(ble_nus_evt_t *p_evt);

/**@brief Function for assert macro callback.
 *
 * @details This function will be called in case of an assert in the SoftDevice.
 *
 * @warning This handler is an example only and does not fit a final product. You need to analyse
 *          how your product is supposed to react in case of Assert.
 * @warning On assert from the SoftDevice, the system can only recover on reset.
 *
 * @param[in] line_num    Line number of the failing ASSERT call.
 * @param[in] p_file_name File name of the failing ASSERT call.
 */
void assert_nrf_callback(uint16_t line_num, const uint8_t *p_file_name);


/**@brief Function for initializing the timer module.
 */
static void timers_init(void) {
  ret_code_t err_code = app_timer_init();
  APP_ERROR_CHECK(err_code);

  // Create timers
  err_code = app_timer_create(&sd_timer_id, APP_TIMER_MODE_REPEATED, timer_sd_handler);
  APP_ERROR_CHECK(err_code);

  err_code = app_timer_create(&main_loop_timer_id, APP_TIMER_MODE_REPEATED, timer_main_loop_handler);
  APP_ERROR_CHECK(err_code);
}


/**@brief Function for the GAP initialization.
 *
 * @details This function will set up all the necessary GAP (Generic Access Profile) parameters of
 *          the device. It also sets the permissions and appearance.
 */
void gap_params_init(void);
// void gap_params_init(void) {
//   uint32_t err_code;
//   ble_gap_conn_params_t gap_conn_params;
//   ble_gap_conn_sec_mode_t sec_mode;

//   BLE_GAP_CONN_SEC_MODE_SET_OPEN(&sec_mode);

//   // get the mac address
//   ble_gap_addr_t addr;
//   err_code = sd_ble_gap_addr_get(&addr);
//   APP_ERROR_CHECK(err_code);

//   // append mac address to the human-readable device name
//   char device_name_with_extension[100];
//   memset(device_name_with_extension, '\0', sizeof(device_name_with_extension));
//   snprintf(device_name_with_extension, sizeof(device_name_with_extension), "%s %02X:%02X:%02X:%02X:%02X:%02X", DEVICE_NAME,
//            addr.addr[5], addr.addr[4], addr.addr[3], addr.addr[2], addr.addr[1], addr.addr[0]);

//   NRF_LOG_INFO("Setting Device Name: [%s]", device_name_with_extension);
//   NRF_LOG_FLUSH();

//   err_code = sd_ble_gap_device_name_set(&sec_mode, (const uint8_t *)device_name_with_extension, strlen(device_name_with_extension));
//   // err_code = sd_ble_gap_device_name_set(&sec_mode, (const uint8_t *)DEVICE_NAME, strlen(DEVICE_NAME));
//   APP_ERROR_CHECK(err_code);

//   memset(&gap_conn_params, 0, sizeof(gap_conn_params));

//   gap_conn_params.min_conn_interval = MIN_CONN_INTERVAL;
//   gap_conn_params.max_conn_interval = MAX_CONN_INTERVAL;
//   gap_conn_params.slave_latency = SLAVE_LATENCY;
//   gap_conn_params.conn_sup_timeout = CONN_SUP_TIMEOUT;

//   err_code = sd_ble_gap_ppcp_set(&gap_conn_params);
//   APP_ERROR_CHECK(err_code);
// }

/**@brief Function for handling Queued Write Module errors.
 *
 * @details A pointer to this function will be passed to each service which may need to inform the
 *          application about an error.
 *
 * @param[in]   nrf_error   Error code containing information about what went wrong.
 */
static void nrf_qwr_error_handler(uint32_t nrf_error) {
  APP_ERROR_HANDLER(nrf_error);
}

/**@brief Function for initializing services that will be used by the application.
 */
static void services_init(void) {
  uint32_t err_code;
  ble_nus_init_t nus_init;
  nrf_ble_qwr_init_t qwr_init = { 0 };

  // Initialize Queued Write Module.
  qwr_init.error_handler = nrf_qwr_error_handler;

  err_code = nrf_ble_qwr_init(&m_qwr, &qwr_init);
  APP_ERROR_CHECK(err_code);

  // Initialize NUS.
  memset(&nus_init, 0, sizeof(nus_init));

  nus_init.data_handler = nus_data_handler;

  err_code = ble_nus_init(&m_nus, &nus_init);
  APP_ERROR_CHECK(err_code);

#ifdef USE_NRF_DFU
  ble_dfu_buttonless_init_t dfus_init = { 0 };
  dfus_init.evt_handler = ble_dfu_evt_handler;

  err_code = ble_dfu_buttonless_init(&dfus_init);
  APP_ERROR_CHECK(err_code);
#endif  // USE_NRF_DFU
}

/**@brief Function for handling an event from the Connection Parameters Module.
 *
 * @details This function will be called for all events in the Connection Parameters Module
 *          which are passed to the application.
 *
 * @note All this function does is to disconnect. This could have been done by simply setting
 *       the disconnect_on_fail config parameter, but instead we use the event handler
 *       mechanism to demonstrate its use.
 *
 * @param[in] p_evt  Event received from the Connection Parameters Module.
 */
static void on_conn_params_evt(ble_conn_params_evt_t *p_evt) {
  uint32_t err_code;

  if (p_evt->evt_type == BLE_CONN_PARAMS_EVT_FAILED) {
    err_code = sd_ble_gap_disconnect(m_conn_handle, BLE_HCI_CONN_INTERVAL_UNACCEPTABLE);
    APP_ERROR_CHECK(err_code);
  }
}


/**@brief Function for handling errors from the Connection Parameters module.
 *
 * @param[in] nrf_error  Error code containing information about what went wrong.
 */
static void conn_params_error_handler(uint32_t nrf_error) {
  APP_ERROR_HANDLER(nrf_error);
}


/**@brief Function for initializing the Connection Parameters module.
 */
static void conn_params_init(void) {
  uint32_t err_code;
  ble_conn_params_init_t cp_init;

  memset(&cp_init, 0, sizeof(cp_init));

  cp_init.p_conn_params = NULL;
  cp_init.first_conn_params_update_delay = FIRST_CONN_PARAMS_UPDATE_DELAY;
  cp_init.next_conn_params_update_delay = NEXT_CONN_PARAMS_UPDATE_DELAY;
  cp_init.max_conn_params_update_count = MAX_CONN_PARAMS_UPDATE_COUNT;
  cp_init.start_on_notify_cccd_handle = BLE_GATT_HANDLE_INVALID;
  cp_init.disconnect_on_fail = false;
  cp_init.evt_handler = on_conn_params_evt;
  cp_init.error_handler = conn_params_error_handler;

  err_code = ble_conn_params_init(&cp_init);
  APP_ERROR_CHECK(err_code);
}


/**@brief Function for putting the chip into sleep mode.
 *
 * @note This function will not return.
 */
static void sleep_mode_enter(void) {
  uint32_t err_code = bsp_indication_set(BSP_INDICATE_IDLE);
  APP_ERROR_CHECK(err_code);

  // Prepare wakeup buttons.
  err_code = bsp_btn_ble_sleep_mode_prepare();
  APP_ERROR_CHECK(err_code);

  // Go to system-off mode (this function will not return; wakeup will cause a reset).
  err_code = sd_power_system_off();
  APP_ERROR_CHECK(err_code);
}


/**@brief Function for handling advertising events.
 *
 * @details This function will be called for advertising events which are passed to the application.
 *
 * @param[in] ble_adv_evt  Advertising event.
 */
static void on_adv_evt(ble_adv_evt_t ble_adv_evt) {
  uint32_t err_code;

  // NRF_LOG_INFO("on_adv_evt [%d]", ble_adv_evt);
  switch (ble_adv_evt) {
    case BLE_ADV_EVT_FAST:
      err_code = bsp_indication_set(BSP_INDICATE_ADVERTISING);
      APP_ERROR_CHECK(err_code);
      // NRF_LOG_INFO("\ton_adv_evt::BLE_ADV_EVT_FAST");
      break;
    case BLE_ADV_EVT_IDLE:
      // NRF_LOG_INFO("\ton_adv_evt::BLE_ADV_EVT_IDLE");
      advertising_start(false);
      // sleep_mode_enter();
      break;
    default:
      break;
  }
}


/**@brief Function for handling BLE events.
 *
 * @param[in]   p_ble_evt   Bluetooth stack event.
 * @param[in]   p_context   Unused.
 */
// void ble_evt_handler(ble_evt_t const *p_ble_evt, void *p_context);


/**@brief Function for handling events from the GATT library. */
void gatt_evt_handler(nrf_ble_gatt_t *p_gatt, nrf_ble_gatt_evt_t const *p_evt);

/**@brief Function for initializing the GATT library. */
void gatt_init(void);


/**@brief Function for handling events from the BSP module.
 *
 * @param[in]   event   Event generated by button press.
 */
void bsp_event_handler(bsp_event_t event);


#ifdef USE_UART
/**@brief   Function for handling app_uart events.
 *
 * @details This function will receive a single character from the app_uart module and append it to
 *          a string. The string will be be sent over BLE when the last character received was a
 *          'new line' '\n' (hex 0x0A) or if the string has reached the maximum data length.
 */
/**@snippet [Handling the data received over UART] */
void uart_event_handle(app_uart_evt_t *p_event) {
  static uint8_t data_array[BLE_NUS_MAX_DATA_LEN];
  static uint8_t index = 0;
  uint32_t err_code;

  switch (p_event->evt_type) {
    case APP_UART_DATA_READY:
      UNUSED_VARIABLE(app_uart_get(&data_array[index]));
      index++;

      if ((data_array[index - 1] == '\n') || (data_array[index - 1] == '\r') || (index >= m_ble_nus_max_data_len)) {
        if (index > 1) {
          NRF_LOG_DEBUG("Ready to send data over BLE NUS");
          NRF_LOG_HEXDUMP_DEBUG(data_array, index);

          do {
            uint16_t length = (uint16_t)index;
            err_code = ble_nus_data_send(&m_nus, data_array, &length, m_conn_handle);
            if ((err_code != NRF_ERROR_INVALID_STATE) && (err_code != NRF_ERROR_RESOURCES) &&
                (err_code != NRF_ERROR_NOT_FOUND)) {
              APP_ERROR_CHECK(err_code);
            }
          } while (err_code == NRF_ERROR_RESOURCES);
        }

        index = 0;
      }
      break;

    case APP_UART_COMMUNICATION_ERROR:
      APP_ERROR_HANDLER(p_event->data.error_communication);
      break;

    case APP_UART_FIFO_ERROR:
      APP_ERROR_HANDLER(p_event->data.error_code);
      break;

    default:
      break;
  }
}
/**@snippet [Handling the data received over UART] */


/**@brief  Function for initializing the UART module.
 */
/**@snippet [UART Initialization] */
static void uart_init(void) {
  uint32_t err_code;
  app_uart_comm_params_t const comm_params = {
    .rx_pin_no = RX_PIN_NUMBER,
    .tx_pin_no = TX_PIN_NUMBER,
    .rts_pin_no = RTS_PIN_NUMBER,
    .cts_pin_no = CTS_PIN_NUMBER,
    .flow_control = APP_UART_FLOW_CONTROL_DISABLED,
    .use_parity = false,
#if defined(UART_PRESENT)
    .baud_rate = NRF_UART_BAUDRATE_115200
#else
    .baud_rate = NRF_UARTE_BAUDRATE_115200
#endif
  };

  APP_UART_FIFO_INIT(&comm_params, UART_RX_BUF_SIZE, UART_TX_BUF_SIZE, uart_event_handle, APP_IRQ_PRIORITY_LOWEST,
                     err_code);
  APP_ERROR_CHECK(err_code);
}
/**@snippet [UART Initialization] */
#endif  // USE_UART


/**@brief Function for initializing the Advertising functionality.
 */
static void advertising_init(void) {
  uint32_t err_code;
  ble_advertising_init_t init;

  memset(&init, 0, sizeof(init));

  init.advdata.name_type = BLE_ADVDATA_FULL_NAME;
  init.advdata.include_appearance = false;
  // init.advdata.flags = BLE_GAP_ADV_FLAGS_LE_ONLY_LIMITED_DISC_MODE;
  init.advdata.flags = BLE_GAP_ADV_FLAGS_LE_ONLY_GENERAL_DISC_MODE;

  init.srdata.uuids_complete.uuid_cnt = sizeof(m_adv_uuids) / sizeof(m_adv_uuids[0]);
  init.srdata.uuids_complete.p_uuids = m_adv_uuids;

  init.config.ble_adv_fast_enabled = true;
  init.config.ble_adv_fast_interval = APP_ADV_INTERVAL;
  init.config.ble_adv_fast_timeout = APP_ADV_DURATION;
  init.evt_handler = on_adv_evt;

  err_code = ble_advertising_init(&m_advertising, &init);
  APP_ERROR_CHECK(err_code);

  ble_advertising_conn_cfg_tag_set(&m_advertising, APP_BLE_CONN_CFG_TAG);
}

/**@brief Function for setting up advertising data. */
static void advertising_data_set(void) {
  ret_code_t ret;

  ble_gap_adv_params_t const adv_params =
    {
        .properties    =
        {
          .type = BLE_GAP_ADV_TYPE_CONNECTABLE_SCANNABLE_UNDIRECTED,
        },
        .p_peer_addr   = NULL,
        .interval      = APP_ADV_INTERVAL,
        .duration      = 0,
        .filter_policy = BLE_GAP_ADV_FP_ANY,

        .primary_phy   = BLE_GAP_PHY_2MBPS, // Must be changed to connect in long range. (BLE_GAP_PHY_CODED)
        .secondary_phy = BLE_GAP_PHY_2MBPS,
    };

  ble_advdata_t const adv_data = {
    .name_type = BLE_ADVDATA_FULL_NAME,
    .include_appearance = false,
    .flags = BLE_GAP_ADV_FLAGS_LE_ONLY_GENERAL_DISC_MODE,
  };

  NRF_LOG_INFO("advertising_data_set() 1");
  NRF_LOG_FLUSH();
  // ret = ble_advdata_encode(&adv_data, m_advertising.adv_data.adv_data.p_data, &m_advertising.adv_data.adv_data.len);
  // APP_ERROR_CHECK(ret);

  NRF_LOG_INFO("advertising_data_set() 2");
  NRF_LOG_FLUSH();

  ret = sd_ble_gap_adv_set_configure(&m_adv_handle, &m_advertising.adv_data, &adv_params);
  APP_ERROR_CHECK(ret);
  
  NRF_LOG_INFO("advertising_data_set() 3");
  NRF_LOG_FLUSH();
}


/**@brief Function for initializing buttons and leds.
 *
 * @param[out] p_erase_bonds  Will be true if the clear bonding button was pressed to wake the application up.
 */
static void buttons_leds_init(bool *p_erase_bonds) {
  bsp_event_t startup_event;

  uint32_t err_code = bsp_init(BSP_INIT_LEDS | BSP_INIT_BUTTONS, bsp_event_handler);
  APP_ERROR_CHECK(err_code);

  err_code = bsp_btn_ble_init(NULL, &startup_event);
  APP_ERROR_CHECK(err_code);

  *p_erase_bonds = (startup_event == BSP_EVENT_CLEAR_BONDING_DATA);
}


/**@brief Function for initializing the nrf log module.
 */
static void log_init(void) {
  ret_code_t err_code = NRF_LOG_INIT(NULL);
  APP_ERROR_CHECK(err_code);

  NRF_LOG_DEFAULT_BACKENDS_INIT();
}


/**@brief Function for initializing power management.
 */
static void power_management_init(void) {
  ret_code_t err_code;
  err_code = nrf_pwr_mgmt_init();
  APP_ERROR_CHECK(err_code);
}

#endif  // YR_UTIL_H_
